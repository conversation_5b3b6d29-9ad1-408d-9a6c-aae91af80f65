{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  // All selected options for navigation indicators\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  onQuestionNavigate,\n  // New prop for question navigation\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data\n  const questionData = question ? extractQuestionData(question) : null;\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Enhanced safety check with debugging\n  if (!question || !questionData) {\n    console.warn('🚨 ModernQuizRenderer: Missing question data', {\n      question: question,\n      questionData: questionData,\n      questionIndex,\n      totalQuestions\n    });\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-blue-500 text-4xl mb-4\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Loading Question...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Please wait while the question loads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500 bg-gray-50 p-3 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Debug Info:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Question Index: \", questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Question Data: \", question ? 'Present' : 'Missing']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Extracted Data: \", questionData ? 'Present' : 'Missing']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Additional validation for question content\n  if (!questionData.name || questionData.name === 'Question not available') {\n    console.warn('🚨 ModernQuizRenderer: Invalid question content', questionData);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-1.5 bg-gray-200/60\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\",\n            style: {\n              width: `${progressPercentage}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\",\n                  children: examTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 font-medium\",\n                  children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${isTimeWarning ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200' : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: `w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-mono tracking-wider\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-xl\",\n                  children: questionIndex + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-white font-bold text-xl sm:text-2xl\",\n                  children: [\"Question \", questionIndex + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100 text-sm font-medium\",\n                  children: [\"of \", totalQuestions, \" questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/90 text-sm font-medium\",\n                children: \"Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-lg\",\n                children: [Math.round(progressPercentage), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 sm:p-8 lg:p-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-3 py-1 rounded-full text-xs font-semibold ${questionData.type === 'mcq' ? 'bg-blue-100 text-blue-700' : questionData.type === 'fill' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`,\n                  children: questionData.type === 'mcq' ? 'Multiple Choice' : questionData.type === 'fill' ? 'Free Text Answer' : 'Question'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 text-green-600 text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 25\n                  }, this), \"Answered\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\",\n                children: questionData.name || 'Loading question...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 mb-8\",\n            children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400 bg-gray-50 p-2 rounded\",\n              children: [\"Debug: Type=\", questionData.type, \", Options=\", Object.keys(questionData.options || {}).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this), questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ?\n            // Multiple Choice Questions\n            Object.entries(questionData.options).map(([key, value], index) => {\n              const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n              const label = optionLabels[index] || key;\n              const isSelected = currentAnswer === key;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleAnswerSelect(key),\n                className: `group cursor-pointer transition-all duration-300 ${isSelected ? 'transform scale-[1.02]' : 'hover:transform hover:scale-[1.01]'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${isSelected ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${isSelected ? 'bg-white/20 text-white' : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'}`,\n                      children: label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex-1 font-medium text-base sm:text-lg leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 29\n                    }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 232,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 25\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 23\n              }, this);\n            }) : questionData.type === 'fill' || questionData.type === 'text' ?\n            /*#__PURE__*/\n            // Free Text Input Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                  children: \"Your Answer:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: currentAnswer,\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-4 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 resize-none text-gray-800 font-medium\",\n                  rows: 4,\n                  style: {\n                    minHeight: '120px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\uD83D\\uDCA1 Tip: Be clear and concise in your answer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [currentAnswer.length, \" characters\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            // No options available\n            _jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400 text-4xl mb-4\",\n                children: \"\\uD83D\\uDCDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: [\"Question type: \", questionData.type || 'Unknown']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400 text-sm mt-2\",\n                children: \"No answer options available for this question.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), \"Question Navigator\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2\",\n              children: Array.from({\n                length: totalQuestions\n              }, (_, i) => {\n                const isCurrentQuestion = i === questionIndex;\n                const isAnswered = selectedOptions[i] && selectedOptions[i].toString().trim() !== '';\n                return /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (typeof onQuestionNavigate === 'function') {\n                      onQuestionNavigate(i);\n                    }\n                  },\n                  className: `w-10 h-10 rounded-lg font-bold text-sm transition-all duration-300 hover:scale-110 ${isCurrentQuestion ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg scale-110' : isAnswered ? 'bg-green-500 text-white shadow-md hover:bg-green-600' : 'bg-white text-gray-600 border border-gray-300 hover:bg-blue-50 hover:border-blue-300'}`,\n                  title: `Question ${i + 1}${isCurrentQuestion ? ' (Current)' : ''}${isAnswered ? ' (Answered)' : ''}`,\n                  children: i + 1\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-6 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Current\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 bg-green-500 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Answered\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 bg-white border border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Not Answered\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [Object.keys(selectedOptions).filter(key => selectedOptions[key] && selectedOptions[key].toString().trim() !== '').length, \" of \", totalQuestions, \" answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between pt-6 border-t border-gray-200 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onPrevious,\n              disabled: questionIndex === 0,\n              className: `flex items-center gap-2 px-4 sm:px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg hover:scale-105'}`,\n              type: \"button\",\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 px-4\",\n              children: [Array.from({\n                length: Math.min(totalQuestions, 10)\n              }, (_, i) => {\n                // Show only first 10 questions as dots, or all if less than 10\n                const actualIndex = totalQuestions <= 10 ? i : Math.floor(i * totalQuestions / 10);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-3 h-3 rounded-full transition-all duration-300 ${actualIndex === questionIndex ? 'bg-blue-500 scale-125' : actualIndex < questionIndex ? 'bg-green-500' : 'bg-gray-300'}`,\n                  title: `Question ${actualIndex + 1}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 23\n                }, this);\n              }), totalQuestions > 10 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500 ml-2\",\n                children: [questionIndex + 1, \"/\", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onNext,\n              className: \"flex items-center gap-2 px-4 sm:px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105\",\n              type: \"button\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: questionIndex === totalQuestions - 1 ? 'Finish' : 'Next'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)]\n  }, `modern-quiz-${renderKey}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernQuizRenderer, \"PH6vAwz56JboJBKDi/YYMWcz/c4=\");\n_c = ModernQuizRenderer;\nexport default ModernQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"ModernQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbBrain", "TbTarget", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "ModernQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "onQuestionNavigate", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionData", "name", "console", "log", "prev", "handleAnswerSelect", "answer", "progressPercentage", "warn", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "Math", "round", "type", "image", "src", "alt", "process", "env", "NODE_ENV", "Object", "keys", "options", "length", "entries", "map", "key", "value", "index", "optionLabels", "label", "isSelected", "onClick", "onChange", "e", "target", "placeholder", "rows", "minHeight", "Array", "from", "_", "i", "isCurrentQuestion", "toString", "trim", "title", "filter", "disabled", "min", "actualIndex", "floor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, Tb<PERSON>heck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {}, // All selected options for navigation indicators\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  onQuestionNavigate, // New prop for question navigation\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data\n  const questionData = question ? extractQuestionData(question) : null;\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Enhanced safety check with debugging\n  if (!question || !questionData) {\n    console.warn('🚨 ModernQuizRenderer: Missing question data', {\n      question: question,\n      questionData: questionData,\n      questionIndex,\n      totalQuestions\n    });\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\">\n          <div className=\"text-blue-500 text-4xl mb-4\">⏳</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Loading Question...</h3>\n          <p className=\"text-gray-600 mb-4\">Please wait while the question loads.</p>\n          <div className=\"text-sm text-gray-500 bg-gray-50 p-3 rounded-lg\">\n            <p>Debug Info:</p>\n            <p>Question Index: {questionIndex + 1} of {totalQuestions}</p>\n            <p>Question Data: {question ? 'Present' : 'Missing'}</p>\n            <p>Extracted Data: {questionData ? 'Present' : 'Missing'}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Additional validation for question content\n  if (!questionData.name || questionData.name === 'Question not available') {\n    console.warn('🚨 ModernQuizRenderer: Invalid question content', questionData);\n  }\n\n  return (\n    <div key={`modern-quiz-${renderKey}`} className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Modern Sticky Header */}\n      <div className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Progress Bar */}\n          <div className=\"w-full h-1.5 bg-gray-200/60\">\n            <div\n              className=\"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\"\n              style={{ width: `${progressPercentage}%` }}\n            />\n          </div>\n          \n          {/* Header Content */}\n          <div className=\"flex items-center justify-between py-4\">\n            {/* Left: Quiz Info */}\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\">\n                  <TbBrain className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h1 className=\"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\">\n                    {examTitle}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 font-medium\">\n                    Question {questionIndex + 1} of {totalQuestions}\n                  </p>\n                </div>\n              </div>\n            </div>\n            \n            {/* Right: Timer */}\n            <div\n              className={`flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${\n                isTimeWarning\n                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200'\n                  : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'\n              }`}\n            >\n              <TbClock className={`w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`} />\n              <span className=\"text-sm font-mono tracking-wider\">{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Quiz Content */}\n      <div className=\"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500\">\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\">\n                    <span className=\"text-white font-bold text-xl\">{questionIndex + 1}</span>\n                  </div>\n                  <div>\n                    <h2 className=\"text-white font-bold text-xl sm:text-2xl\">Question {questionIndex + 1}</h2>\n                    <p className=\"text-blue-100 text-sm font-medium\">of {totalQuestions} questions</p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-white/90 text-sm font-medium\">Progress</div>\n                  <div className=\"text-white font-bold text-lg\">{Math.round(progressPercentage)}%</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Question Content */}\n            <div className=\"p-6 sm:p-8 lg:p-10\">\n              {/* Question Text */}\n              <div className=\"mb-8\">\n                <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                      questionData.type === 'mcq'\n                        ? 'bg-blue-100 text-blue-700'\n                        : questionData.type === 'fill'\n                        ? 'bg-green-100 text-green-700'\n                        : 'bg-gray-100 text-gray-700'\n                    }`}>\n                      {questionData.type === 'mcq' ? 'Multiple Choice' :\n                       questionData.type === 'fill' ? 'Free Text Answer' :\n                       'Question'}\n                    </div>\n                    {isAnswered && (\n                      <div className=\"flex items-center gap-1 text-green-600 text-sm font-medium\">\n                        <TbCheck className=\"w-4 h-4\" />\n                        Answered\n                      </div>\n                    )}\n                  </div>\n                  <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\">\n                    {questionData.name || 'Loading question...'}\n                  </h3>\n                </div>\n              </div>\n\n              {/* Question Image */}\n              {questionData.image && (\n                <div className=\"mb-8 text-center\">\n                  <div className=\"inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm\">\n                    <img\n                      src={questionData.image}\n                      alt=\"Question\"\n                      className=\"max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Answer Options */}\n              <div className=\"space-y-4 mb-8\">\n                {/* Debug info for question type */}\n                {process.env.NODE_ENV === 'development' && (\n                  <div className=\"text-xs text-gray-400 bg-gray-50 p-2 rounded\">\n                    Debug: Type={questionData.type}, Options={Object.keys(questionData.options || {}).length}\n                  </div>\n                )}\n\n                {questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ? (\n                  // Multiple Choice Questions\n                  Object.entries(questionData.options).map(([key, value], index) => {\n                    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                    const label = optionLabels[index] || key;\n                    const isSelected = currentAnswer === key;\n\n                    return (\n                      <div\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`group cursor-pointer transition-all duration-300 ${\n                          isSelected\n                            ? 'transform scale-[1.02]'\n                            : 'hover:transform hover:scale-[1.01]'\n                        }`}\n                      >\n                        <div className={`p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${\n                          isSelected\n                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200'\n                            : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'\n                        }`}>\n                          <div className=\"flex items-center gap-4\">\n                            <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${\n                              isSelected\n                                ? 'bg-white/20 text-white'\n                                : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'\n                            }`}>\n                              {label}\n                            </div>\n                            <span className=\"flex-1 font-medium text-base sm:text-lg leading-relaxed\">{value}</span>\n                            {isSelected && (\n                              <div className=\"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\">\n                                <TbCheck className=\"w-4 h-4 text-white\" />\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })\n                ) : questionData.type === 'fill' || questionData.type === 'text' ? (\n                  // Free Text Input Questions\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200\">\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                        Your Answer:\n                      </label>\n                      <textarea\n                        value={currentAnswer}\n                        onChange={(e) => handleAnswerSelect(e.target.value)}\n                        placeholder=\"Type your answer here...\"\n                        className=\"w-full p-4 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 resize-none text-gray-800 font-medium\"\n                        rows={4}\n                        style={{ minHeight: '120px' }}\n                      />\n                      <div className=\"mt-3 flex items-center justify-between\">\n                        <p className=\"text-sm text-gray-600\">\n                          💡 Tip: Be clear and concise in your answer\n                        </p>\n                        <span className=\"text-sm text-gray-500\">\n                          {currentAnswer.length} characters\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ) : (\n                  // No options available\n                  <div className=\"text-center py-8\">\n                    <div className=\"text-gray-400 text-4xl mb-4\">📝</div>\n                    <p className=\"text-gray-500\">\n                      Question type: {questionData.type || 'Unknown'}\n                    </p>\n                    <p className=\"text-gray-400 text-sm mt-2\">\n                      No answer options available for this question.\n                    </p>\n                  </div>\n                )}\n              </div>\n\n              {/* Question Navigation Grid */}\n              <div className=\"mb-6 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200\">\n                <h4 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\">\n                  <TbTarget className=\"w-5 h-5 text-blue-600\" />\n                  Question Navigator\n                </h4>\n                <div className=\"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2\">\n                  {Array.from({ length: totalQuestions }, (_, i) => {\n                    const isCurrentQuestion = i === questionIndex;\n                    const isAnswered = selectedOptions[i] && selectedOptions[i].toString().trim() !== '';\n\n                    return (\n                      <button\n                        key={i}\n                        onClick={() => {\n                          if (typeof onQuestionNavigate === 'function') {\n                            onQuestionNavigate(i);\n                          }\n                        }}\n                        className={`w-10 h-10 rounded-lg font-bold text-sm transition-all duration-300 hover:scale-110 ${\n                          isCurrentQuestion\n                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg scale-110'\n                            : isAnswered\n                            ? 'bg-green-500 text-white shadow-md hover:bg-green-600'\n                            : 'bg-white text-gray-600 border border-gray-300 hover:bg-blue-50 hover:border-blue-300'\n                        }`}\n                        title={`Question ${i + 1}${isCurrentQuestion ? ' (Current)' : ''}${isAnswered ? ' (Answered)' : ''}`}\n                      >\n                        {i + 1}\n                      </button>\n                    );\n                  })}\n                </div>\n                <div className=\"mt-4 flex items-center justify-between\">\n                  <div className=\"flex items-center gap-6 text-sm\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded\"></div>\n                      <span className=\"text-gray-600\">Current</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-4 h-4 bg-green-500 rounded\"></div>\n                      <span className=\"text-gray-600\">Answered</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-4 h-4 bg-white border border-gray-300 rounded\"></div>\n                      <span className=\"text-gray-600\">Not Answered</span>\n                    </div>\n                  </div>\n                  <div className=\"text-sm text-gray-600\">\n                    {Object.keys(selectedOptions).filter(key => selectedOptions[key] && selectedOptions[key].toString().trim() !== '').length} of {totalQuestions} answered\n                  </div>\n                </div>\n              </div>\n\n              {/* Navigation Buttons */}\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200 mt-6\">\n                {/* Previous Button */}\n                <button\n                  onClick={onPrevious}\n                  disabled={questionIndex === 0}\n                  className={`flex items-center gap-2 px-4 sm:px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                    questionIndex === 0\n                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg hover:scale-105'\n                  }`}\n                  type=\"button\"\n                >\n                  <TbArrowLeft className=\"w-5 h-5\" />\n                  <span className=\"hidden sm:inline\">Previous</span>\n                </button>\n\n                {/* Progress Dots */}\n                <div className=\"flex items-center gap-2 px-4\">\n                  {Array.from({ length: Math.min(totalQuestions, 10) }, (_, i) => {\n                    // Show only first 10 questions as dots, or all if less than 10\n                    const actualIndex = totalQuestions <= 10 ? i : Math.floor((i * totalQuestions) / 10);\n                    return (\n                      <div\n                        key={i}\n                        className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                          actualIndex === questionIndex\n                            ? 'bg-blue-500 scale-125'\n                            : actualIndex < questionIndex\n                            ? 'bg-green-500'\n                            : 'bg-gray-300'\n                        }`}\n                        title={`Question ${actualIndex + 1}`}\n                      />\n                    );\n                  })}\n                  {totalQuestions > 10 && (\n                    <span className=\"text-xs text-gray-500 ml-2\">\n                      {questionIndex + 1}/{totalQuestions}\n                    </span>\n                  )}\n                </div>\n\n                {/* Next/Finish Button */}\n                <button\n                  onClick={onNext}\n                  className=\"flex items-center gap-2 px-4 sm:px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105\"\n                  type=\"button\"\n                >\n                  <span className=\"hidden sm:inline\">\n                    {questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'}\n                  </span>\n                  <span className=\"sm:hidden\">\n                    {questionIndex === totalQuestions - 1 ? 'Finish' : 'Next'}\n                  </span>\n                  {questionIndex === totalQuestions - 1 ? (\n                    <TbTarget className=\"w-5 h-5\" />\n                  ) : (\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/F,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EAAE;EACtBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,kBAAkB;EAAE;EACpBC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAACiB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAMiC,YAAY,GAAGnB,QAAQ,GAAGN,mBAAmB,CAACM,QAAQ,CAAC,GAAG,IAAI;EAEpEb,SAAS,CAAC,MAAM;IACd2B,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACAd,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,IAAImB,YAAY,IAAIA,YAAY,CAACC,IAAI,EAAE;MACjDC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9EJ,YAAY,CAACK,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACvB,QAAQ,EAAEmB,YAAY,CAAC,CAAC;EAE5B,MAAMK,kBAAkB,GAAIC,MAAM,IAAK;IACrCX,gBAAgB,CAACW,MAAM,CAAC;IACxBT,aAAa,CAAC,IAAI,CAAC;IACnBX,cAAc,CAACoB,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACzB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACmB,YAAY,EAAE;IAC9BE,OAAO,CAACM,IAAI,CAAC,8CAA8C,EAAE;MAC3D3B,QAAQ,EAAEA,QAAQ;MAClBmB,YAAY,EAAEA,YAAY;MAC1BlB,aAAa;MACbC;IACF,CAAC,CAAC;IAEF,oBACEJ,OAAA;MAAK8B,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG/B,OAAA;QAAK8B,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC9E/B,OAAA;UAAK8B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDnC,OAAA;UAAI8B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFnC,OAAA;UAAG8B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3EnC,OAAA;UAAK8B,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9D/B,OAAA;YAAA+B,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClBnC,OAAA;YAAA+B,QAAA,GAAG,kBAAgB,EAAC5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DnC,OAAA;YAAA+B,QAAA,GAAG,iBAAe,EAAC7B,QAAQ,GAAG,SAAS,GAAG,SAAS;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDnC,OAAA;YAAA+B,QAAA,GAAG,kBAAgB,EAACV,YAAY,GAAG,SAAS,GAAG,SAAS;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACd,YAAY,CAACC,IAAI,IAAID,YAAY,CAACC,IAAI,KAAK,wBAAwB,EAAE;IACxEC,OAAO,CAACM,IAAI,CAAC,iDAAiD,EAAER,YAAY,CAAC;EAC/E;EAEA,oBACErB,OAAA;IAAsC8B,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAEtH/B,OAAA;MAAK8B,SAAS,EAAC,sFAAsF;MAAAC,QAAA,eACnG/B,OAAA;QAAK8B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD/B,OAAA;UAAK8B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C/B,OAAA;YACE8B,SAAS,EAAC,sHAAsH;YAChIM,KAAK,EAAE;cAAEC,KAAK,EAAG,GAAET,kBAAmB;YAAG;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD/B,OAAA;YAAK8B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtC/B,OAAA;cAAK8B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/B,OAAA;gBAAK8B,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAC3H/B,OAAA,CAACN,OAAO;kBAACoC,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNnC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAI8B,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,EACrFnB;gBAAS;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLnC,OAAA;kBAAG8B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,WACtC,EAAC5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YACE8B,SAAS,EAAG,wFACVjB,aAAa,GACT,+EAA+E,GAC/E,uFACL,EAAE;YAAAkB,QAAA,gBAEH/B,OAAA,CAACV,OAAO;cAACwC,SAAS,EAAG,WAAUjB,aAAa,GAAG,eAAe,GAAG,EAAG;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzEnC,OAAA;cAAM8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEjC,UAAU,CAACU,QAAQ;YAAC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D/B,OAAA;QAAK8B,SAAS,EAAC,wHAAwH;QAAAC,QAAA,gBAEnI/B,OAAA;UAAK8B,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC5F/B,OAAA;YAAK8B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/B,OAAA;cAAK8B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/B,OAAA;gBAAK8B,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,eAClG/B,OAAA;kBAAM8B,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAE5B,aAAa,GAAG;gBAAC;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACNnC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAI8B,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,WAAS,EAAC5B,aAAa,GAAG,CAAC;gBAAA;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1FnC,OAAA;kBAAG8B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,KAAG,EAAC3B,cAAc,EAAC,YAAU;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnC,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/B,OAAA;gBAAK8B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjEnC,OAAA;gBAAK8B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GAAEO,IAAI,CAACC,KAAK,CAACX,kBAAkB,CAAC,EAAC,GAAC;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAEjC/B,OAAA;YAAK8B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/B,OAAA;cAAK8B,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAClH/B,OAAA;gBAAK8B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/B,OAAA;kBAAK8B,SAAS,EAAG,gDACfT,YAAY,CAACmB,IAAI,KAAK,KAAK,GACvB,2BAA2B,GAC3BnB,YAAY,CAACmB,IAAI,KAAK,MAAM,GAC5B,6BAA6B,GAC7B,2BACL,EAAE;kBAAAT,QAAA,EACAV,YAAY,CAACmB,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC/CnB,YAAY,CAACmB,IAAI,KAAK,MAAM,GAAG,kBAAkB,GACjD;gBAAU;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,EACLlB,UAAU,iBACTjB,OAAA;kBAAK8B,SAAS,EAAC,4DAA4D;kBAAAC,QAAA,gBACzE/B,OAAA,CAACP,OAAO;oBAACqC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNnC,OAAA;gBAAI8B,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EACxFV,YAAY,CAACC,IAAI,IAAI;cAAqB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLd,YAAY,CAACoB,KAAK,iBACjBzC,OAAA;YAAK8B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B/B,OAAA;cAAK8B,SAAS,EAAC,0EAA0E;cAAAC,QAAA,eACvF/B,OAAA;gBACE0C,GAAG,EAAErB,YAAY,CAACoB,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACdb,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnC,OAAA;YAAK8B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAE5Ba,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC9C,OAAA;cAAK8B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,cAChD,EAACV,YAAY,CAACmB,IAAI,EAAC,YAAU,EAACO,MAAM,CAACC,IAAI,CAAC3B,YAAY,CAAC4B,OAAO,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CACN,EAEAd,YAAY,CAACmB,IAAI,KAAK,KAAK,IAAInB,YAAY,CAAC4B,OAAO,IAAIF,MAAM,CAACC,IAAI,CAAC3B,YAAY,CAAC4B,OAAO,CAAC,CAACC,MAAM,GAAG,CAAC;YAClG;YACAH,MAAM,CAACI,OAAO,CAAC9B,YAAY,CAAC4B,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;cAChE,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACnD,MAAMC,KAAK,GAAGD,YAAY,CAACD,KAAK,CAAC,IAAIF,GAAG;cACxC,MAAMK,UAAU,GAAG3C,aAAa,KAAKsC,GAAG;cAExC,oBACErD,OAAA;gBAEE2D,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC2B,GAAG,CAAE;gBACvCvB,SAAS,EAAG,oDACV4B,UAAU,GACN,wBAAwB,GACxB,oCACL,EAAE;gBAAA3B,QAAA,eAEH/B,OAAA;kBAAK8B,SAAS,EAAG,+DACf4B,UAAU,GACN,mGAAmG,GACnG,yGACL,EAAE;kBAAA3B,QAAA,eACD/B,OAAA;oBAAK8B,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtC/B,OAAA;sBAAK8B,SAAS,EAAG,uGACf4B,UAAU,GACN,wBAAwB,GACxB,mDACL,EAAE;sBAAA3B,QAAA,EACA0B;oBAAK;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnC,OAAA;sBAAM8B,SAAS,EAAC,yDAAyD;sBAAAC,QAAA,EAAEuB;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACvFuB,UAAU,iBACT1D,OAAA;sBAAK8B,SAAS,EAAC,mEAAmE;sBAAAC,QAAA,eAChF/B,OAAA,CAACP,OAAO;wBAACqC,SAAS,EAAC;sBAAoB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA5BDkB,GAAG;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BL,CAAC;YAEV,CAAC,CAAC,GACAd,YAAY,CAACmB,IAAI,KAAK,MAAM,IAAInB,YAAY,CAACmB,IAAI,KAAK,MAAM;YAAA;YAC9D;YACAxC,OAAA;cAAK8B,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxB/B,OAAA;gBAAK8B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,gBAChG/B,OAAA;kBAAO8B,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnC,OAAA;kBACEsD,KAAK,EAAEvC,aAAc;kBACrB6C,QAAQ,EAAGC,CAAC,IAAKnC,kBAAkB,CAACmC,CAAC,CAACC,MAAM,CAACR,KAAK,CAAE;kBACpDS,WAAW,EAAC,0BAA0B;kBACtCjC,SAAS,EAAC,yKAAyK;kBACnLkC,IAAI,EAAE,CAAE;kBACR5B,KAAK,EAAE;oBAAE6B,SAAS,EAAE;kBAAQ;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACFnC,OAAA;kBAAK8B,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/B,OAAA;oBAAG8B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJnC,OAAA;oBAAM8B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACpChB,aAAa,CAACmC,MAAM,EAAC,aACxB;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAnC,OAAA;cAAK8B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/B,OAAA;gBAAK8B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrDnC,OAAA;gBAAG8B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,iBACZ,EAACV,YAAY,CAACmB,IAAI,IAAI,SAAS;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACJnC,OAAA;gBAAG8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnG/B,OAAA;cAAI8B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAC9E/B,OAAA,CAACL,QAAQ;gBAACmC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnC,OAAA;cAAK8B,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EACnFmC,KAAK,CAACC,IAAI,CAAC;gBAAEjB,MAAM,EAAE9C;cAAe,CAAC,EAAE,CAACgE,CAAC,EAAEC,CAAC,KAAK;gBAChD,MAAMC,iBAAiB,GAAGD,CAAC,KAAKlE,aAAa;gBAC7C,MAAMc,UAAU,GAAGX,eAAe,CAAC+D,CAAC,CAAC,IAAI/D,eAAe,CAAC+D,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;gBAEpF,oBACExE,OAAA;kBAEE2D,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,OAAOhD,kBAAkB,KAAK,UAAU,EAAE;sBAC5CA,kBAAkB,CAAC0D,CAAC,CAAC;oBACvB;kBACF,CAAE;kBACFvC,SAAS,EAAG,sFACVwC,iBAAiB,GACb,6EAA6E,GAC7ErD,UAAU,GACV,sDAAsD,GACtD,sFACL,EAAE;kBACHwD,KAAK,EAAG,YAAWJ,CAAC,GAAG,CAAE,GAAEC,iBAAiB,GAAG,YAAY,GAAG,EAAG,GAAErD,UAAU,GAAG,aAAa,GAAG,EAAG,EAAE;kBAAAc,QAAA,EAEpGsC,CAAC,GAAG;gBAAC,GAfDA,CAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgBA,CAAC;cAEb,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnC,OAAA;cAAK8B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/B,OAAA;gBAAK8B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C/B,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC/B,OAAA;oBAAK8B,SAAS,EAAC;kBAA8D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFnC,OAAA;oBAAM8B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNnC,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC/B,OAAA;oBAAK8B,SAAS,EAAC;kBAA8B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDnC,OAAA;oBAAM8B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNnC,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC/B,OAAA;oBAAK8B,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEnC,OAAA;oBAAM8B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnC,OAAA;gBAAK8B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnCgB,MAAM,CAACC,IAAI,CAAC1C,eAAe,CAAC,CAACoE,MAAM,CAACrB,GAAG,IAAI/C,eAAe,CAAC+C,GAAG,CAAC,IAAI/C,eAAe,CAAC+C,GAAG,CAAC,CAACkB,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAACtB,MAAM,EAAC,MAAI,EAAC9C,cAAc,EAAC,WAChJ;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEnF/B,OAAA;cACE2D,OAAO,EAAEjD,UAAW;cACpBiE,QAAQ,EAAExE,aAAa,KAAK,CAAE;cAC9B2B,SAAS,EAAG,kGACV3B,aAAa,KAAK,CAAC,GACf,yDAAyD,GACzD,uFACL,EAAE;cACHqC,IAAI,EAAC,QAAQ;cAAAT,QAAA,gBAEb/B,OAAA,CAACT,WAAW;gBAACuC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCnC,OAAA;gBAAM8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAGTnC,OAAA;cAAK8B,SAAS,EAAC,8BAA8B;cAAAC,QAAA,GAC1CmC,KAAK,CAACC,IAAI,CAAC;gBAAEjB,MAAM,EAAEZ,IAAI,CAACsC,GAAG,CAACxE,cAAc,EAAE,EAAE;cAAE,CAAC,EAAE,CAACgE,CAAC,EAAEC,CAAC,KAAK;gBAC9D;gBACA,MAAMQ,WAAW,GAAGzE,cAAc,IAAI,EAAE,GAAGiE,CAAC,GAAG/B,IAAI,CAACwC,KAAK,CAAET,CAAC,GAAGjE,cAAc,GAAI,EAAE,CAAC;gBACpF,oBACEJ,OAAA;kBAEE8B,SAAS,EAAG,oDACV+C,WAAW,KAAK1E,aAAa,GACzB,uBAAuB,GACvB0E,WAAW,GAAG1E,aAAa,GAC3B,cAAc,GACd,aACL,EAAE;kBACHsE,KAAK,EAAG,YAAWI,WAAW,GAAG,CAAE;gBAAE,GARhCR,CAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASP,CAAC;cAEN,CAAC,CAAC,EACD/B,cAAc,GAAG,EAAE,iBAClBJ,OAAA;gBAAM8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GACzC5B,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNnC,OAAA;cACE2D,OAAO,EAAElD,MAAO;cAChBqB,SAAS,EAAC,0OAA0O;cACpPU,IAAI,EAAC,QAAQ;cAAAT,QAAA,gBAEb/B,OAAA;gBAAM8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC/B5B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;cAAM;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACPnC,OAAA;gBAAM8B,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACxB5B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;cAAM;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACNhC,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA,CAACL,QAAQ;gBAACmC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEhCnC,OAAA,CAACR,YAAY;gBAACsC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,GA9TG,eAAchB,SAAU,EAAC;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA+T/B,CAAC;AAEV,CAAC;AAACrB,EAAA,CA3YIb,kBAAkB;AAAA8E,EAAA,GAAlB9E,kBAAkB;AA6YxB,eAAeA,kBAAkB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}