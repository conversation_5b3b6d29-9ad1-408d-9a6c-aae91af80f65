{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  // All selected options for navigation indicators\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  onQuestionNavigate,\n  // New prop for question navigation\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data\n  const questionData = question ? extractQuestionData(question) : null;\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Safety check\n  if (!question || !questionData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-blue-500 text-4xl mb-4\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Loading Question...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please wait while the question loads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-1.5 bg-gray-200/60\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\",\n            style: {\n              width: `${progressPercentage}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\",\n                  children: examTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 font-medium\",\n                  children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${isTimeWarning ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200' : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: `w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-mono tracking-wider\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-xl\",\n                  children: questionIndex + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-white font-bold text-xl sm:text-2xl\",\n                  children: [\"Question \", questionIndex + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100 text-sm font-medium\",\n                  children: [\"of \", totalQuestions, \" questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/90 text-sm font-medium\",\n                children: \"Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-lg\",\n                children: [Math.round(progressPercentage), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 sm:p-8 lg:p-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\",\n                children: questionData.name || 'Loading question...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 mb-8\",\n            children: questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ?\n            // Multiple Choice Questions\n            Object.entries(questionData.options).map(([key, value], index) => {\n              const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n              const label = optionLabels[index] || key;\n              const isSelected = currentAnswer === key;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleAnswerSelect(key),\n                className: `group cursor-pointer transition-all duration-300 ${isSelected ? 'transform scale-[1.02]' : 'hover:transform hover:scale-[1.01]'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${isSelected ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${isSelected ? 'bg-white/20 text-white' : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'}`,\n                      children: label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex-1 font-medium text-base sm:text-lg leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 29\n                    }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 25\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 23\n              }, this);\n            }) : questionData.type === 'fill' || questionData.type === 'text' ?\n            /*#__PURE__*/\n            // Free Text Input Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                  children: \"Your Answer:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: currentAnswer,\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-4 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 resize-none text-gray-800 font-medium\",\n                  rows: 4,\n                  style: {\n                    minHeight: '120px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\uD83D\\uDCA1 Tip: Be clear and concise in your answer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [currentAnswer.length, \" characters\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            // No options available\n            _jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400 text-4xl mb-4\",\n                children: \"\\uD83D\\uDCDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: [\"Question type: \", questionData.type || 'Unknown']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400 text-sm mt-2\",\n                children: \"No answer options available for this question.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), \"Question Navigator\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2\",\n              children: Array.from({\n                length: totalQuestions\n              }, (_, i) => {\n                const isCurrentQuestion = i === questionIndex;\n                const isAnswered = selectedAnswer && i === questionIndex; // You might want to track all answered questions\n\n                return /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add navigation to specific question\n                    if (typeof onQuestionNavigate === 'function') {\n                      onQuestionNavigate(i);\n                    }\n                  },\n                  className: `w-10 h-10 rounded-lg font-bold text-sm transition-all duration-300 hover:scale-110 ${isCurrentQuestion ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg scale-110' : isAnswered ? 'bg-green-500 text-white shadow-md hover:bg-green-600' : 'bg-white text-gray-600 border border-gray-300 hover:bg-blue-50 hover:border-blue-300'}`,\n                  title: `Question ${i + 1}${isCurrentQuestion ? ' (Current)' : ''}${isAnswered ? ' (Answered)' : ''}`,\n                  children: i + 1\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex items-center gap-6 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Current\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 bg-green-500 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Answered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 bg-white border border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Not Answered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between pt-6 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onPrevious,\n              disabled: questionIndex === 0,\n              className: `flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: Array.from({\n                length: totalQuestions\n              }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full transition-all duration-300 ${i === questionIndex ? 'bg-blue-500 scale-125' : i < questionIndex ? 'bg-green-500' : 'bg-gray-300'}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onNext,\n              className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, `modern-quiz-${renderKey}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernQuizRenderer, \"PH6vAwz56JboJBKDi/YYMWcz/c4=\");\n_c = ModernQuizRenderer;\nexport default ModernQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"ModernQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbBrain", "TbTarget", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "ModernQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "onQuestionNavigate", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionData", "name", "console", "log", "prev", "handleAnswerSelect", "answer", "progressPercentage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "Math", "round", "image", "src", "alt", "type", "options", "Object", "keys", "length", "entries", "map", "key", "value", "index", "optionLabels", "label", "isSelected", "onClick", "onChange", "e", "target", "placeholder", "rows", "minHeight", "Array", "from", "_", "i", "isCurrentQuestion", "title", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, Tb<PERSON>heck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {}, // All selected options for navigation indicators\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  onQuestionNavigate, // New prop for question navigation\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data\n  const questionData = question ? extractQuestionData(question) : null;\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Safety check\n  if (!question || !questionData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-blue-500 text-4xl mb-4\">⏳</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Loading Question...</h3>\n          <p className=\"text-gray-600\">Please wait while the question loads.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div key={`modern-quiz-${renderKey}`} className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Modern Sticky Header */}\n      <div className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Progress Bar */}\n          <div className=\"w-full h-1.5 bg-gray-200/60\">\n            <div\n              className=\"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\"\n              style={{ width: `${progressPercentage}%` }}\n            />\n          </div>\n          \n          {/* Header Content */}\n          <div className=\"flex items-center justify-between py-4\">\n            {/* Left: Quiz Info */}\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\">\n                  <TbBrain className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h1 className=\"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\">\n                    {examTitle}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 font-medium\">\n                    Question {questionIndex + 1} of {totalQuestions}\n                  </p>\n                </div>\n              </div>\n            </div>\n            \n            {/* Right: Timer */}\n            <div\n              className={`flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${\n                isTimeWarning\n                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200'\n                  : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'\n              }`}\n            >\n              <TbClock className={`w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`} />\n              <span className=\"text-sm font-mono tracking-wider\">{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Quiz Content */}\n      <div className=\"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500\">\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\">\n                    <span className=\"text-white font-bold text-xl\">{questionIndex + 1}</span>\n                  </div>\n                  <div>\n                    <h2 className=\"text-white font-bold text-xl sm:text-2xl\">Question {questionIndex + 1}</h2>\n                    <p className=\"text-blue-100 text-sm font-medium\">of {totalQuestions} questions</p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-white/90 text-sm font-medium\">Progress</div>\n                  <div className=\"text-white font-bold text-lg\">{Math.round(progressPercentage)}%</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Question Content */}\n            <div className=\"p-6 sm:p-8 lg:p-10\">\n              {/* Question Text */}\n              <div className=\"mb-8\">\n                <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\">\n                  <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\">\n                    {questionData.name || 'Loading question...'}\n                  </h3>\n                </div>\n              </div>\n\n              {/* Question Image */}\n              {questionData.image && (\n                <div className=\"mb-8 text-center\">\n                  <div className=\"inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm\">\n                    <img\n                      src={questionData.image}\n                      alt=\"Question\"\n                      className=\"max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Answer Options */}\n              <div className=\"space-y-4 mb-8\">\n                {questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ? (\n                  // Multiple Choice Questions\n                  Object.entries(questionData.options).map(([key, value], index) => {\n                    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                    const label = optionLabels[index] || key;\n                    const isSelected = currentAnswer === key;\n\n                    return (\n                      <div\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`group cursor-pointer transition-all duration-300 ${\n                          isSelected\n                            ? 'transform scale-[1.02]'\n                            : 'hover:transform hover:scale-[1.01]'\n                        }`}\n                      >\n                        <div className={`p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${\n                          isSelected\n                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200'\n                            : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'\n                        }`}>\n                          <div className=\"flex items-center gap-4\">\n                            <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${\n                              isSelected\n                                ? 'bg-white/20 text-white'\n                                : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'\n                            }`}>\n                              {label}\n                            </div>\n                            <span className=\"flex-1 font-medium text-base sm:text-lg leading-relaxed\">{value}</span>\n                            {isSelected && (\n                              <div className=\"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\">\n                                <TbCheck className=\"w-4 h-4 text-white\" />\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })\n                ) : questionData.type === 'fill' || questionData.type === 'text' ? (\n                  // Free Text Input Questions\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200\">\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                        Your Answer:\n                      </label>\n                      <textarea\n                        value={currentAnswer}\n                        onChange={(e) => handleAnswerSelect(e.target.value)}\n                        placeholder=\"Type your answer here...\"\n                        className=\"w-full p-4 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 resize-none text-gray-800 font-medium\"\n                        rows={4}\n                        style={{ minHeight: '120px' }}\n                      />\n                      <div className=\"mt-3 flex items-center justify-between\">\n                        <p className=\"text-sm text-gray-600\">\n                          💡 Tip: Be clear and concise in your answer\n                        </p>\n                        <span className=\"text-sm text-gray-500\">\n                          {currentAnswer.length} characters\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ) : (\n                  // No options available\n                  <div className=\"text-center py-8\">\n                    <div className=\"text-gray-400 text-4xl mb-4\">📝</div>\n                    <p className=\"text-gray-500\">\n                      Question type: {questionData.type || 'Unknown'}\n                    </p>\n                    <p className=\"text-gray-400 text-sm mt-2\">\n                      No answer options available for this question.\n                    </p>\n                  </div>\n                )}\n              </div>\n\n              {/* Question Navigation Grid */}\n              <div className=\"mb-6 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200\">\n                <h4 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\">\n                  <TbTarget className=\"w-5 h-5 text-blue-600\" />\n                  Question Navigator\n                </h4>\n                <div className=\"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2\">\n                  {Array.from({ length: totalQuestions }, (_, i) => {\n                    const isCurrentQuestion = i === questionIndex;\n                    const isAnswered = selectedAnswer && i === questionIndex; // You might want to track all answered questions\n\n                    return (\n                      <button\n                        key={i}\n                        onClick={() => {\n                          // Add navigation to specific question\n                          if (typeof onQuestionNavigate === 'function') {\n                            onQuestionNavigate(i);\n                          }\n                        }}\n                        className={`w-10 h-10 rounded-lg font-bold text-sm transition-all duration-300 hover:scale-110 ${\n                          isCurrentQuestion\n                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg scale-110'\n                            : isAnswered\n                            ? 'bg-green-500 text-white shadow-md hover:bg-green-600'\n                            : 'bg-white text-gray-600 border border-gray-300 hover:bg-blue-50 hover:border-blue-300'\n                        }`}\n                        title={`Question ${i + 1}${isCurrentQuestion ? ' (Current)' : ''}${isAnswered ? ' (Answered)' : ''}`}\n                      >\n                        {i + 1}\n                      </button>\n                    );\n                  })}\n                </div>\n                <div className=\"mt-4 flex items-center gap-6 text-sm\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded\"></div>\n                    <span className=\"text-gray-600\">Current</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 bg-green-500 rounded\"></div>\n                    <span className=\"text-gray-600\">Answered</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 bg-white border border-gray-300 rounded\"></div>\n                    <span className=\"text-gray-600\">Not Answered</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Navigation Buttons */}\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200\">\n                <button\n                  onClick={onPrevious}\n                  disabled={questionIndex === 0}\n                  className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 ${\n                    questionIndex === 0\n                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg'\n                  }`}\n                >\n                  <TbArrowLeft className=\"w-5 h-5\" />\n                  <span className=\"hidden sm:inline\">Previous</span>\n                </button>\n\n                <div className=\"flex items-center gap-2\">\n                  {Array.from({ length: totalQuestions }, (_, i) => (\n                    <div\n                      key={i}\n                      className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                        i === questionIndex\n                          ? 'bg-blue-500 scale-125'\n                          : i < questionIndex\n                          ? 'bg-green-500'\n                          : 'bg-gray-300'\n                      }`}\n                    />\n                  ))}\n                </div>\n\n                <button\n                  onClick={onNext}\n                  className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105\"\n                >\n                  <span>{questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'}</span>\n                  {questionIndex === totalQuestions - 1 ? (\n                    <TbTarget className=\"w-5 h-5\" />\n                  ) : (\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/F,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EAAE;EACtBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,kBAAkB;EAAE;EACpBC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAACiB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAMiC,YAAY,GAAGnB,QAAQ,GAAGN,mBAAmB,CAACM,QAAQ,CAAC,GAAG,IAAI;EAEpEb,SAAS,CAAC,MAAM;IACd2B,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACAd,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,IAAImB,YAAY,IAAIA,YAAY,CAACC,IAAI,EAAE;MACjDC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9EJ,YAAY,CAACK,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACvB,QAAQ,EAAEmB,YAAY,CAAC,CAAC;EAE5B,MAAMK,kBAAkB,GAAIC,MAAM,IAAK;IACrCX,gBAAgB,CAACW,MAAM,CAAC;IACxBT,aAAa,CAAC,IAAI,CAAC;IACnBX,cAAc,CAACoB,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACzB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACmB,YAAY,EAAE;IAC9B,oBACErB,OAAA;MAAK6B,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG9B,OAAA;QAAK6B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D9B,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDlC,OAAA;UAAI6B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFlC,OAAA;UAAG6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAsC6B,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAEtH9B,OAAA;MAAK6B,SAAS,EAAC,sFAAsF;MAAAC,QAAA,eACnG9B,OAAA;QAAK6B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD9B,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C9B,OAAA;YACE6B,SAAS,EAAC,sHAAsH;YAChIM,KAAK,EAAE;cAAEC,KAAK,EAAG,GAAER,kBAAmB;YAAG;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD9B,OAAA;YAAK6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtC9B,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAK6B,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAC3H9B,OAAA,CAACN,OAAO;kBAACmC,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI6B,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,EACrFlB;gBAAS;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLlC,OAAA;kBAAG6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,WACtC,EAAC3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlC,OAAA;YACE6B,SAAS,EAAG,wFACVhB,aAAa,GACT,+EAA+E,GAC/E,uFACL,EAAE;YAAAiB,QAAA,gBAEH9B,OAAA,CAACV,OAAO;cAACuC,SAAS,EAAG,WAAUhB,aAAa,GAAG,eAAe,GAAG,EAAG;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzElC,OAAA;cAAM6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEhC,UAAU,CAACU,QAAQ;YAAC;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D9B,OAAA;QAAK6B,SAAS,EAAC,wHAAwH;QAAAC,QAAA,gBAEnI9B,OAAA;UAAK6B,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC5F9B,OAAA;YAAK6B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9B,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAK6B,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,eAClG9B,OAAA;kBAAM6B,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAE3B,aAAa,GAAG;gBAAC;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI6B,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,WAAS,EAAC3B,aAAa,GAAG,CAAC;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1FlC,OAAA;kBAAG6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,KAAG,EAAC1B,cAAc,EAAC,YAAU;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjElC,OAAA;gBAAK6B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GAAEO,IAAI,CAACC,KAAK,CAACV,kBAAkB,CAAC,EAAC,GAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAEjC9B,OAAA;YAAK6B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB9B,OAAA;cAAK6B,SAAS,EAAC,qGAAqG;cAAAC,QAAA,eAClH9B,OAAA;gBAAI6B,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EACxFT,YAAY,CAACC,IAAI,IAAI;cAAqB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLb,YAAY,CAACkB,KAAK,iBACjBvC,OAAA;YAAK6B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B9B,OAAA;cAAK6B,SAAS,EAAC,0EAA0E;cAAAC,QAAA,eACvF9B,OAAA;gBACEwC,GAAG,EAAEnB,YAAY,CAACkB,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACdZ,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDlC,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BT,YAAY,CAACqB,IAAI,KAAK,KAAK,IAAIrB,YAAY,CAACsB,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACxB,YAAY,CAACsB,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC;YAClG;YACAF,MAAM,CAACG,OAAO,CAAC1B,YAAY,CAACsB,OAAO,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;cAChE,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACnD,MAAMC,KAAK,GAAGD,YAAY,CAACD,KAAK,CAAC,IAAIF,GAAG;cACxC,MAAMK,UAAU,GAAGvC,aAAa,KAAKkC,GAAG;cAExC,oBACEjD,OAAA;gBAEEuD,OAAO,EAAEA,CAAA,KAAM7B,kBAAkB,CAACuB,GAAG,CAAE;gBACvCpB,SAAS,EAAG,oDACVyB,UAAU,GACN,wBAAwB,GACxB,oCACL,EAAE;gBAAAxB,QAAA,eAEH9B,OAAA;kBAAK6B,SAAS,EAAG,+DACfyB,UAAU,GACN,mGAAmG,GACnG,yGACL,EAAE;kBAAAxB,QAAA,eACD9B,OAAA;oBAAK6B,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtC9B,OAAA;sBAAK6B,SAAS,EAAG,uGACfyB,UAAU,GACN,wBAAwB,GACxB,mDACL,EAAE;sBAAAxB,QAAA,EACAuB;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlC,OAAA;sBAAM6B,SAAS,EAAC,yDAAyD;sBAAAC,QAAA,EAAEoB;oBAAK;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACvFoB,UAAU,iBACTtD,OAAA;sBAAK6B,SAAS,EAAC,mEAAmE;sBAAAC,QAAA,eAChF9B,OAAA,CAACP,OAAO;wBAACoC,SAAS,EAAC;sBAAoB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA5BDe,GAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BL,CAAC;YAEV,CAAC,CAAC,GACAb,YAAY,CAACqB,IAAI,KAAK,MAAM,IAAIrB,YAAY,CAACqB,IAAI,KAAK,MAAM;YAAA;YAC9D;YACA1C,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxB9B,OAAA;gBAAK6B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,gBAChG9B,OAAA;kBAAO6B,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlC,OAAA;kBACEkD,KAAK,EAAEnC,aAAc;kBACrByC,QAAQ,EAAGC,CAAC,IAAK/B,kBAAkB,CAAC+B,CAAC,CAACC,MAAM,CAACR,KAAK,CAAE;kBACpDS,WAAW,EAAC,0BAA0B;kBACtC9B,SAAS,EAAC,yKAAyK;kBACnL+B,IAAI,EAAE,CAAE;kBACRzB,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAQ;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACFlC,OAAA;kBAAK6B,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD9B,OAAA;oBAAG6B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJlC,OAAA;oBAAM6B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACpCf,aAAa,CAAC+B,MAAM,EAAC,aACxB;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAlC,OAAA;cAAK6B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9B,OAAA;gBAAK6B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrDlC,OAAA;gBAAG6B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,iBACZ,EAACT,YAAY,CAACqB,IAAI,IAAI,SAAS;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACJlC,OAAA;gBAAG6B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnG9B,OAAA;cAAI6B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAC9E9B,OAAA,CAACL,QAAQ;gBAACkC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlC,OAAA;cAAK6B,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EACnFgC,KAAK,CAACC,IAAI,CAAC;gBAAEjB,MAAM,EAAE1C;cAAe,CAAC,EAAE,CAAC4D,CAAC,EAAEC,CAAC,KAAK;gBAChD,MAAMC,iBAAiB,GAAGD,CAAC,KAAK9D,aAAa;gBAC7C,MAAMc,UAAU,GAAGZ,cAAc,IAAI4D,CAAC,KAAK9D,aAAa,CAAC,CAAC;;gBAE1D,oBACEH,OAAA;kBAEEuD,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACA,IAAI,OAAO5C,kBAAkB,KAAK,UAAU,EAAE;sBAC5CA,kBAAkB,CAACsD,CAAC,CAAC;oBACvB;kBACF,CAAE;kBACFpC,SAAS,EAAG,sFACVqC,iBAAiB,GACb,6EAA6E,GAC7EjD,UAAU,GACV,sDAAsD,GACtD,sFACL,EAAE;kBACHkD,KAAK,EAAG,YAAWF,CAAC,GAAG,CAAE,GAAEC,iBAAiB,GAAG,YAAY,GAAG,EAAG,GAAEjD,UAAU,GAAG,aAAa,GAAG,EAAG,EAAE;kBAAAa,QAAA,EAEpGmC,CAAC,GAAG;gBAAC,GAhBDA,CAAC;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBA,CAAC;cAEb,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnD9B,OAAA;gBAAK6B,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC9B,OAAA;kBAAK6B,SAAS,EAAC;gBAA8D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpFlC,OAAA;kBAAM6B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC9B,OAAA;kBAAK6B,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDlC,OAAA;kBAAM6B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC9B,OAAA;kBAAK6B,SAAS,EAAC;gBAAiD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvElC,OAAA;kBAAM6B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC9E9B,OAAA;cACEuD,OAAO,EAAE7C,UAAW;cACpB0D,QAAQ,EAAEjE,aAAa,KAAK,CAAE;cAC9B0B,SAAS,EAAG,0GACV1B,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,uEACL,EAAE;cAAA2B,QAAA,gBAEH9B,OAAA,CAACT,WAAW;gBAACsC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnClC,OAAA;gBAAM6B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAETlC,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrCgC,KAAK,CAACC,IAAI,CAAC;gBAAEjB,MAAM,EAAE1C;cAAe,CAAC,EAAE,CAAC4D,CAAC,EAAEC,CAAC,kBAC3CjE,OAAA;gBAEE6B,SAAS,EAAG,oDACVoC,CAAC,KAAK9D,aAAa,GACf,uBAAuB,GACvB8D,CAAC,GAAG9D,aAAa,GACjB,cAAc,GACd,aACL;cAAE,GAPE8D,CAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQP,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlC,OAAA;cACEuD,OAAO,EAAE9C,MAAO;cAChBoB,SAAS,EAAC,kOAAkO;cAAAC,QAAA,gBAE5O9B,OAAA;gBAAA8B,QAAA,EAAO3B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;cAAM;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC3E/B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA,CAACL,QAAQ;gBAACkC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEhClC,OAAA,CAACR,YAAY;gBAACqC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,GA5QG,eAAcf,SAAU,EAAC;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA6Q/B,CAAC;AAEV,CAAC;AAACpB,EAAA,CAvUIb,kBAAkB;AAAAoE,EAAA,GAAlBpE,kBAAkB;AAyUxB,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}