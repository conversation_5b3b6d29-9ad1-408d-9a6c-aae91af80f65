{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  var _questions$selectedQu, _activeQuestion$name;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const [forceUpdate, setForceUpdate] = useState(0);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getExamData = async (retryCount = 0) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2, _response$data3;\n        const questionsArray = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || [];\n        console.log('🔍 Quiz Data Debug:', {\n          examId: id,\n          examName: (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.name,\n          totalQuestions: questionsArray.length,\n          firstQuestion: questionsArray[0],\n          questionStructure: questionsArray.map(q => ({\n            id: q === null || q === void 0 ? void 0 : q._id,\n            name: q === null || q === void 0 ? void 0 : q.name,\n            question: q === null || q === void 0 ? void 0 : q.question,\n            type: q === null || q === void 0 ? void 0 : q.type,\n            answerType: q === null || q === void 0 ? void 0 : q.answerType,\n            hasOptions: q !== null && q !== void 0 && q.options ? Object.keys(q.options).length : 0\n          }))\n        });\n\n        // Check if questions are properly populated\n        if (questionsArray.length === 0) {\n          console.warn('No questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        // Validate question structure with more detailed checking\n        const validQuestions = questionsArray.filter(q => {\n          if (!q) return false;\n\n          // Check if question has text\n          const hasQuestionText = q.name || q.question || q.text;\n          if (!hasQuestionText) {\n            console.warn('Question missing text:', q);\n            return false;\n          }\n\n          // For MCQ questions, check if they have options\n          if ((q.type === 'mcq' || q.answerType === 'Options') && (!q.options || Object.keys(q.options).length === 0)) {\n            console.warn('MCQ question missing options:', q);\n            return false;\n          }\n          return true;\n        });\n        console.log('✅ Valid questions found:', validQuestions.length);\n        if (validQuestions.length === 0) {\n          console.warn('No valid questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n        setQuestions(validQuestions);\n        setExamData(response.data);\n        setSecondsLeft((((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.duration) || 0) * 60);\n\n        // Multiple force re-renders to ensure questions display\n        setTimeout(() => setForceUpdate(prev => prev + 1), 50);\n        setTimeout(() => setForceUpdate(prev => prev + 1), 200);\n        setTimeout(() => setForceUpdate(prev => prev + 1), 500);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {\n        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);\n        setTimeout(() => getExamData(retryCount + 1), 1000);\n        return;\n      }\n      message.error(error.message || 'Failed to load quiz. Please try again.');\n      navigate('/user/quiz');\n    }\n  };\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        var _response$xpData, _response$xpData2, _response$xpData3, _response$xpData4;\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: ((_response$xpData = response.xpData) === null || _response$xpData === void 0 ? void 0 : _response$xpData.xpAwarded) || 0,\n            newTotalXP: ((_response$xpData2 = response.xpData) === null || _response$xpData2 === void 0 ? void 0 : _response$xpData2.newTotalXP) || 0,\n            levelUp: ((_response$xpData3 = response.xpData) === null || _response$xpData3 === void 0 ? void 0 : _response$xpData3.levelUp) || false,\n            newLevel: ((_response$xpData4 = response.xpData) === null || _response$xpData4 === void 0 ? void 0 : _response$xpData4.newLevel) || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            result: resultWithXP\n          }\n        });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n  const startTimer = useCallback(() => {\n    const totalSeconds = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []);\n\n  // Force update when questions are loaded\n  useEffect(() => {\n    if (questions.length > 0) {\n      console.log('🔄 Questions loaded, forcing update...');\n      setForceUpdate(prev => prev + 1);\n    }\n  }, [questions.length]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n\n  // Loading state - wait for both examData AND questions to be loaded\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: !examData ? 'Loading quiz data...' : 'Loading questions...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-500\",\n          children: [\"Debug: examData=\", examData ? 'loaded' : 'null', \", questions=\", questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle case where selectedQuestionIndex is out of bounds\n  if (selectedQuestionIndex >= questions.length) {\n    setSelectedQuestionIndex(0);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Adjusting question index...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Current question validation\n  const currentQuestion = questions[selectedQuestionIndex];\n  if (!currentQuestion) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Question Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"The current question could not be loaded.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedQuestionIndex(0),\n            className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors\",\n            children: \"Go to First Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"w-full bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Final debug before rendering\n  console.log('🎯 Final QuizPlay render state:', {\n    examData: examData ? {\n      name: examData.name,\n      id: examData._id\n    } : null,\n    questionsCount: questions.length,\n    selectedQuestionIndex,\n    currentQuestion: questions[selectedQuestionIndex] ? {\n      id: questions[selectedQuestionIndex]._id,\n      name: ((_questions$selectedQu = questions[selectedQuestionIndex].name) === null || _questions$selectedQu === void 0 ? void 0 : _questions$selectedQu.substring(0, 50)) + '...',\n      type: questions[selectedQuestionIndex].type,\n      hasOptions: questions[selectedQuestionIndex].options ? Object.keys(questions[selectedQuestionIndex].options).length : 0\n    } : null,\n    timeLeft: secondsLeft\n  });\n\n  // Simple test component to bypass complex rendering\n  const activeQuestion = questions[selectedQuestionIndex];\n  const mountKey = `quiz-${examData === null || examData === void 0 ? void 0 : examData._id}-${selectedQuestionIndex}-${forceUpdate}-${questions.length}`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-100 border border-yellow-400 p-4 rounded-lg mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-bold text-yellow-800\",\n        children: \"\\uD83D\\uDD0D Debug Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Questions loaded:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 12\n        }, this), \" \", questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Current question index:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 12\n        }, this), \" \", selectedQuestionIndex]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Current question exists:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 12\n        }, this), \" \", activeQuestion ? 'YES' : 'NO']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Force update count:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 12\n        }, this), \" \", forceUpdate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Mount key:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 12\n        }, this), \" \", mountKey]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), activeQuestion && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Question ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 16\n          }, this), \" \", activeQuestion._id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Question text:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 16\n          }, this), \" \", (_activeQuestion$name = activeQuestion.name) === null || _activeQuestion$name === void 0 ? void 0 : _activeQuestion$name.substring(0, 100), \"...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this), activeQuestion ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg p-6 shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-bold mb-4 text-gray-900\",\n        children: [\"Question \", selectedQuestionIndex + 1, \" of \", questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-lg mb-6 p-4 bg-gray-50 rounded border\",\n        children: activeQuestion.name || 'Question text not available'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 11\n      }, this), activeQuestion.options && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: Object.entries(activeQuestion.options).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 border rounded hover:bg-blue-50 cursor-pointer\",\n          onClick: () => setSelectedOptions({\n            ...selectedOptions,\n            [selectedQuestionIndex]: key\n          }),\n          style: {\n            backgroundColor: selectedOptions[selectedQuestionIndex] === key ? '#dbeafe' : 'white',\n            borderColor: selectedOptions[selectedQuestionIndex] === key ? '#3b82f6' : '#d1d5db'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [key, \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 19\n          }, this), \" \", value]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between mt-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedQuestionIndex(Math.max(0, selectedQuestionIndex - 1)),\n          disabled: selectedQuestionIndex === 0,\n          className: \"px-4 py-2 bg-gray-500 text-white rounded disabled:opacity-50\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            if (selectedQuestionIndex === questions.length - 1) {\n              calculateResult();\n            } else {\n              setSelectedQuestionIndex(selectedQuestionIndex + 1);\n            }\n          },\n          className: \"px-4 py-2 bg-blue-600 text-white rounded\",\n          children: selectedQuestionIndex === questions.length - 1 ? 'Finish' : 'Next'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 p-6 rounded-lg text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-red-800 font-bold\",\n        children: \"No Question Available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-600\",\n        children: [\"Question \", selectedQuestionIndex + 1, \" could not be loaded.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 9\n    }, this)]\n  }, mountKey, true, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"UpuqtjONhY3bhnSwak3om9EbzXg=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "getExamById", "addReport", "HideLoading", "ShowLoading", "chatWithChatGPTToGetAns", "Quiz<PERSON><PERSON><PERSON>", "QuizError<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "_questions$selectedQu", "_activeQuestion$name", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "startTime", "setStartTime", "forceUpdate", "setForceUpdate", "id", "navigate", "dispatch", "user", "state", "getExamData", "retryCount", "response", "examId", "success", "_response$data", "_response$data2", "_response$data3", "questionsArray", "data", "console", "log", "examName", "name", "totalQuestions", "length", "firstQuestion", "questionStructure", "map", "q", "_id", "question", "type", "answerType", "hasOptions", "options", "Object", "keys", "warn", "validQuestions", "filter", "hasQuestionText", "text", "duration", "setTimeout", "prev", "error", "code", "checkFreeTextAnswers", "payload", "calculateResult", "freeTextPayload", "for<PERSON>ach", "idx", "push", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "result", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "timeSpent", "Math", "floor", "Date", "now", "totalTimeAllowed", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "_response$xpData", "_response$xpData2", "_response$xpData3", "_response$xpData4", "localStorage", "removeItem", "window", "dispatchEvent", "CustomEvent", "detail", "userId", "xpGained", "xpData", "xpAwarded", "newTotalXP", "levelUp", "newLevel", "currentLevel", "resultWithXP", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentQuestion", "onClick", "questionsCount", "substring", "timeLeft", "activeQuestion", "<PERSON><PERSON><PERSON>", "entries", "key", "value", "style", "backgroundColor", "borderColor", "max", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\n\nconst QuizPlay = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const [forceUpdate, setForceUpdate] = useState(0);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  const getExamData = async (retryCount = 0) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({ examId: id });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        const questionsArray = response.data?.questions || [];\n\n        console.log('🔍 Quiz Data Debug:', {\n          examId: id,\n          examName: response.data?.name,\n          totalQuestions: questionsArray.length,\n          firstQuestion: questionsArray[0],\n          questionStructure: questionsArray.map(q => ({\n            id: q?._id,\n            name: q?.name,\n            question: q?.question,\n            type: q?.type,\n            answerType: q?.answerType,\n            hasOptions: q?.options ? Object.keys(q.options).length : 0\n          }))\n        });\n\n        // Check if questions are properly populated\n        if (questionsArray.length === 0) {\n          console.warn('No questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        // Validate question structure with more detailed checking\n        const validQuestions = questionsArray.filter(q => {\n          if (!q) return false;\n\n          // Check if question has text\n          const hasQuestionText = q.name || q.question || q.text;\n          if (!hasQuestionText) {\n            console.warn('Question missing text:', q);\n            return false;\n          }\n\n          // For MCQ questions, check if they have options\n          if ((q.type === 'mcq' || q.answerType === 'Options') && (!q.options || Object.keys(q.options).length === 0)) {\n            console.warn('MCQ question missing options:', q);\n            return false;\n          }\n\n          return true;\n        });\n\n        console.log('✅ Valid questions found:', validQuestions.length);\n\n        if (validQuestions.length === 0) {\n          console.warn('No valid questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        setQuestions(validQuestions);\n        setExamData(response.data);\n        setSecondsLeft((response.data?.duration || 0) * 60);\n\n        // Multiple force re-renders to ensure questions display\n        setTimeout(() => setForceUpdate(prev => prev + 1), 50);\n        setTimeout(() => setForceUpdate(prev => prev + 1), 200);\n        setTimeout(() => setForceUpdate(prev => prev + 1), 500);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {\n        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);\n        setTimeout(() => getExamData(retryCount + 1), 1000);\n        return;\n      }\n\n      message.error(error.message || 'Failed to load quiz. Please try again.');\n      navigate('/user/quiz');\n    }\n  };\n\n  const checkFreeTextAnswers = async (payload) => {\n    if (!payload.length) return [];\n    const { data } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n\n      dispatch(ShowLoading());\n\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\",\n          });\n        }\n      });\n\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n\n      gptResults.forEach((r) => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\n        }\n      });\n\n      const correctAnswers = [];\n      const wrongAnswers = [];\n\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = { ...q, userAnswer: userAnswerKey };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = (examData?.duration || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id,\n      });\n\n      if (response.success) {\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: response.xpData?.xpAwarded || 0,\n            newTotalXP: response.xpData?.newTotalXP || 0,\n            levelUp: response.xpData?.levelUp || false,\n            newLevel: response.xpData?.newLevel || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, { state: { result: resultWithXP } });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n\n  const startTimer = useCallback(() => {\n    const totalSeconds = (examData?.duration || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft((prevSeconds) => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []);\n\n  // Force update when questions are loaded\n  useEffect(() => {\n    if (questions.length > 0) {\n      console.log('🔄 Questions loaded, forcing update...');\n      setForceUpdate(prev => prev + 1);\n    }\n  }, [questions.length]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n\n  // Loading state - wait for both examData AND questions to be loaded\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">\n            {!examData ? 'Loading quiz data...' : 'Loading questions...'}\n          </p>\n          <p className=\"mt-2 text-sm text-gray-500\">\n            Debug: examData={examData ? 'loaded' : 'null'}, questions={questions.length}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  // Handle case where selectedQuestionIndex is out of bounds\n  if (selectedQuestionIndex >= questions.length) {\n    setSelectedQuestionIndex(0);\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Adjusting question index...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Current question validation\n  const currentQuestion = questions[selectedQuestionIndex];\n  if (!currentQuestion) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Question Not Found</h3>\n          <p className=\"text-gray-600 mb-6\">The current question could not be loaded.</p>\n          <div className=\"space-y-3\">\n            <button\n              onClick={() => setSelectedQuestionIndex(0)}\n              className=\"w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors\"\n            >\n              Go to First Question\n            </button>\n            <button\n              onClick={() => navigate('/user/quiz')}\n              className=\"w-full bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Final debug before rendering\n  console.log('🎯 Final QuizPlay render state:', {\n    examData: examData ? { name: examData.name, id: examData._id } : null,\n    questionsCount: questions.length,\n    selectedQuestionIndex,\n    currentQuestion: questions[selectedQuestionIndex] ? {\n      id: questions[selectedQuestionIndex]._id,\n      name: questions[selectedQuestionIndex].name?.substring(0, 50) + '...',\n      type: questions[selectedQuestionIndex].type,\n      hasOptions: questions[selectedQuestionIndex].options ? Object.keys(questions[selectedQuestionIndex].options).length : 0\n    } : null,\n    timeLeft: secondsLeft\n  });\n\n  // Simple test component to bypass complex rendering\n  const activeQuestion = questions[selectedQuestionIndex];\n  const mountKey = `quiz-${examData?._id}-${selectedQuestionIndex}-${forceUpdate}-${questions.length}`;\n\n  return (\n    <div key={mountKey} className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8\">\n      {/* Debug Panel */}\n      <div className=\"bg-yellow-100 border border-yellow-400 p-4 rounded-lg mb-6\">\n        <h3 className=\"font-bold text-yellow-800\">🔍 Debug Information</h3>\n        <p><strong>Questions loaded:</strong> {questions.length}</p>\n        <p><strong>Current question index:</strong> {selectedQuestionIndex}</p>\n        <p><strong>Current question exists:</strong> {activeQuestion ? 'YES' : 'NO'}</p>\n        <p><strong>Force update count:</strong> {forceUpdate}</p>\n        <p><strong>Mount key:</strong> {mountKey}</p>\n        {activeQuestion && (\n          <div className=\"mt-2\">\n            <p><strong>Question ID:</strong> {activeQuestion._id}</p>\n            <p><strong>Question text:</strong> {activeQuestion.name?.substring(0, 100)}...</p>\n          </div>\n        )}\n      </div>\n\n      {/* Simple Question Display */}\n      {activeQuestion ? (\n        <div className=\"bg-white rounded-lg p-6 shadow-lg\">\n          <h2 className=\"text-xl font-bold mb-4 text-gray-900\">\n            Question {selectedQuestionIndex + 1} of {questions.length}\n          </h2>\n\n          <div className=\"text-lg mb-6 p-4 bg-gray-50 rounded border\">\n            {activeQuestion.name || 'Question text not available'}\n          </div>\n\n          {/* Simple Options Display */}\n          {activeQuestion.options && (\n            <div className=\"space-y-3\">\n              {Object.entries(activeQuestion.options).map(([key, value]) => (\n                <div\n                  key={key}\n                  className=\"p-3 border rounded hover:bg-blue-50 cursor-pointer\"\n                  onClick={() => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: key})}\n                  style={{\n                    backgroundColor: selectedOptions[selectedQuestionIndex] === key ? '#dbeafe' : 'white',\n                    borderColor: selectedOptions[selectedQuestionIndex] === key ? '#3b82f6' : '#d1d5db'\n                  }}\n                >\n                  <strong>{key}:</strong> {value}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {/* Simple Navigation */}\n          <div className=\"flex justify-between mt-6\">\n            <button\n              onClick={() => setSelectedQuestionIndex(Math.max(0, selectedQuestionIndex - 1))}\n              disabled={selectedQuestionIndex === 0}\n              className=\"px-4 py-2 bg-gray-500 text-white rounded disabled:opacity-50\"\n            >\n              Previous\n            </button>\n            <button\n              onClick={() => {\n                if (selectedQuestionIndex === questions.length - 1) {\n                  calculateResult();\n                } else {\n                  setSelectedQuestionIndex(selectedQuestionIndex + 1);\n                }\n              }}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded\"\n            >\n              {selectedQuestionIndex === questions.length - 1 ? 'Finish' : 'Next'}\n            </button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"bg-red-100 border border-red-400 p-6 rounded-lg text-center\">\n          <h3 className=\"text-red-800 font-bold\">No Question Available</h3>\n          <p className=\"text-red-600\">Question {selectedQuestionIndex + 1} could not be loaded.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,iBAAiB,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM;IAAEuC;EAAG,CAAC,GAAGpC,SAAS,CAAC,CAAC;EAC1B,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,WAAW,GAAG,MAAAA,CAAOC,UAAU,GAAG,CAAC,KAAK;IAC5C,IAAI;MACFJ,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMmC,QAAQ,GAAG,MAAMtC,WAAW,CAAC;QAAEuC,MAAM,EAAER;MAAG,CAAC,CAAC;MAClDE,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIoC,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA;QACpB,MAAMC,cAAc,GAAG,EAAAH,cAAA,GAAAH,QAAQ,CAACO,IAAI,cAAAJ,cAAA,uBAAbA,cAAA,CAAe1B,SAAS,KAAI,EAAE;QAErD+B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;UACjCR,MAAM,EAAER,EAAE;UACViB,QAAQ,GAAAN,eAAA,GAAEJ,QAAQ,CAACO,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeO,IAAI;UAC7BC,cAAc,EAAEN,cAAc,CAACO,MAAM;UACrCC,aAAa,EAAER,cAAc,CAAC,CAAC,CAAC;UAChCS,iBAAiB,EAAET,cAAc,CAACU,GAAG,CAACC,CAAC,KAAK;YAC1CxB,EAAE,EAAEwB,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEC,GAAG;YACVP,IAAI,EAAEM,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEN,IAAI;YACbQ,QAAQ,EAAEF,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEE,QAAQ;YACrBC,IAAI,EAAEH,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEG,IAAI;YACbC,UAAU,EAAEJ,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEI,UAAU;YACzBC,UAAU,EAAEL,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEM,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACR,CAAC,CAACM,OAAO,CAAC,CAACV,MAAM,GAAG;UAC3D,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA,IAAIP,cAAc,CAACO,MAAM,KAAK,CAAC,EAAE;UAC/BL,OAAO,CAACkB,IAAI,CAAC,kCAAkC,CAAC;UAChDhD,YAAY,CAAC,EAAE,CAAC;UAChBF,WAAW,CAACwB,QAAQ,CAACO,IAAI,CAAC;UAC1B;QACF;;QAEA;QACA,MAAMoB,cAAc,GAAGrB,cAAc,CAACsB,MAAM,CAACX,CAAC,IAAI;UAChD,IAAI,CAACA,CAAC,EAAE,OAAO,KAAK;;UAEpB;UACA,MAAMY,eAAe,GAAGZ,CAAC,CAACN,IAAI,IAAIM,CAAC,CAACE,QAAQ,IAAIF,CAAC,CAACa,IAAI;UACtD,IAAI,CAACD,eAAe,EAAE;YACpBrB,OAAO,CAACkB,IAAI,CAAC,wBAAwB,EAAET,CAAC,CAAC;YACzC,OAAO,KAAK;UACd;;UAEA;UACA,IAAI,CAACA,CAAC,CAACG,IAAI,KAAK,KAAK,IAAIH,CAAC,CAACI,UAAU,KAAK,SAAS,MAAM,CAACJ,CAAC,CAACM,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACR,CAAC,CAACM,OAAO,CAAC,CAACV,MAAM,KAAK,CAAC,CAAC,EAAE;YAC3GL,OAAO,CAACkB,IAAI,CAAC,+BAA+B,EAAET,CAAC,CAAC;YAChD,OAAO,KAAK;UACd;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEFT,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkB,cAAc,CAACd,MAAM,CAAC;QAE9D,IAAIc,cAAc,CAACd,MAAM,KAAK,CAAC,EAAE;UAC/BL,OAAO,CAACkB,IAAI,CAAC,wCAAwC,CAAC;UACtDhD,YAAY,CAAC,EAAE,CAAC;UAChBF,WAAW,CAACwB,QAAQ,CAACO,IAAI,CAAC;UAC1B;QACF;QAEA7B,YAAY,CAACiD,cAAc,CAAC;QAC5BnD,WAAW,CAACwB,QAAQ,CAACO,IAAI,CAAC;QAC1BvB,cAAc,CAAC,CAAC,EAAAqB,eAAA,GAAAL,QAAQ,CAACO,IAAI,cAAAF,eAAA,uBAAbA,eAAA,CAAe0B,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC;;QAEnD;QACAC,UAAU,CAAC,MAAMxC,cAAc,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QACtDD,UAAU,CAAC,MAAMxC,cAAc,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;QACvDD,UAAU,CAAC,MAAMxC,cAAc,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,CAAC,MAAM;QACLxE,OAAO,CAACyE,KAAK,CAAClC,QAAQ,CAACvC,OAAO,CAAC;QAC/BiC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdvC,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,IAAImC,UAAU,GAAG,CAAC,KAAKmC,KAAK,CAACC,IAAI,KAAK,cAAc,IAAI,CAACD,KAAK,CAAClC,QAAQ,CAAC,EAAE;QACxEQ,OAAO,CAACC,GAAG,CAAE,uCAAsCV,UAAU,GAAG,CAAE,EAAC,CAAC;QACpEiC,UAAU,CAAC,MAAMlC,WAAW,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;QACnD;MACF;MAEAtC,OAAO,CAACyE,KAAK,CAACA,KAAK,CAACzE,OAAO,IAAI,wCAAwC,CAAC;MACxEiC,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAM0C,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACxB,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEN;IAAK,CAAC,GAAG,MAAMzC,uBAAuB,CAACuE,OAAO,CAAC;IACvD,OAAO9B,IAAI;EACb,CAAC;EAED,MAAM+B,eAAe,GAAGlF,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,IAAI,CAACwC,IAAI,IAAI,CAACA,IAAI,CAACsB,GAAG,EAAE;QACtBzD,OAAO,CAACyE,KAAK,CAAC,sCAAsC,CAAC;QACrDxC,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAC,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM0E,eAAe,GAAG,EAAE;MAC1B9D,SAAS,CAAC+D,OAAO,CAAC,CAACvB,CAAC,EAAEwB,GAAG,KAAK;QAC5B,IAAIxB,CAAC,CAACG,IAAI,KAAK,MAAM,IAAIH,CAAC,CAACI,UAAU,KAAK,WAAW,IAAIJ,CAAC,CAACI,UAAU,KAAK,mBAAmB,EAAE;UAC7FkB,eAAe,CAACG,IAAI,CAAC;YACnBvB,QAAQ,EAAEF,CAAC,CAACN,IAAI;YAChBgC,cAAc,EAAE1B,CAAC,CAAC2B,aAAa,IAAI3B,CAAC,CAAC4B,aAAa;YAClDC,UAAU,EAAEjE,eAAe,CAAC4D,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMM,UAAU,GAAG,MAAMX,oBAAoB,CAACG,eAAe,CAAC;MAC9D,MAAMS,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACP,OAAO,CAAES,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACC,MAAM,IAAI,OAAOD,CAAC,CAACC,MAAM,CAACC,SAAS,KAAK,SAAS,EAAE;UACvDH,MAAM,CAACC,CAAC,CAAC9B,QAAQ,CAAC,GAAG8B,CAAC,CAACC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOD,CAAC,CAACE,SAAS,KAAK,SAAS,EAAE;UAC3CH,MAAM,CAACC,CAAC,CAAC9B,QAAQ,CAAC,GAAG;YAAEgC,SAAS,EAAEF,CAAC,CAACE,SAAS;YAAEC,MAAM,EAAEH,CAAC,CAACG,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MAEvB7E,SAAS,CAAC+D,OAAO,CAAC,CAACvB,CAAC,EAAEwB,GAAG,KAAK;QAC5B,MAAMc,aAAa,GAAG1E,eAAe,CAAC4D,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAIxB,CAAC,CAACG,IAAI,KAAK,MAAM,IAAIH,CAAC,CAACI,UAAU,KAAK,WAAW,IAAIJ,CAAC,CAACI,UAAU,KAAK,mBAAmB,EAAE;UAC7F,MAAM;YAAE8B,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGJ,MAAM,CAAC/B,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAM6C,QAAQ,GAAG;YAAE,GAAGvC,CAAC;YAAE6B,UAAU,EAAES,aAAa;YAAEH;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACX,IAAI,CAACc,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACZ,IAAI,CAACc,QAAQ,CAAC;UAC7B;QACF,CAAC,MAAM,IAAIvC,CAAC,CAACG,IAAI,KAAK,KAAK,IAAIH,CAAC,CAACI,UAAU,KAAK,SAAS,EAAE;UACzD,MAAMoC,UAAU,GAAGxC,CAAC,CAAC4B,aAAa,IAAI5B,CAAC,CAAC2B,aAAa;UACrD,MAAMO,SAAS,GAAGM,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGvC,CAAC;YAAE6B,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIJ,SAAS,EAAE;YACbE,cAAc,CAACX,IAAI,CAACc,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACZ,IAAI,CAACc,QAAQ,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MAEF,MAAME,SAAS,GAAGrE,SAAS,GAAGsE,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGzE,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAM0E,gBAAgB,GAAG,CAAC,CAAAxF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwD,QAAQ,KAAI,CAAC,IAAI,EAAE;MACvD,MAAMnB,cAAc,GAAGnC,SAAS,CAACoC,MAAM;MACvC,MAAMmD,YAAY,GAAGX,cAAc,CAACxC,MAAM;MAC1C,MAAMoD,eAAe,GAAGN,IAAI,CAACO,KAAK,CAAEF,YAAY,GAAGpD,cAAc,GAAI,GAAG,CAAC;MACzE,MAAMuD,MAAM,GAAGH,YAAY,GAAG,EAAE;;MAEhC;MACA,MAAMI,iBAAiB,GAAG7F,QAAQ,CAAC6F,iBAAiB,IAAI7F,QAAQ,CAAC8F,YAAY,IAAI,EAAE;MACnF,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBlB,cAAc;QACdC,YAAY;QACZgB,OAAO;QACPE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACdvD,cAAc,EAAEA,cAAc;QAC9B8C,SAAS,EAAEA,SAAS;QACpBK,gBAAgB,EAAEA;MACpB,CAAC;MAED,MAAM/D,QAAQ,GAAG,MAAMrC,SAAS,CAAC;QAC/B8G,IAAI,EAAEhF,EAAE;QACRyD,MAAM,EAAEqB,UAAU;QAClB3E,IAAI,EAAEA,IAAI,CAACsB;MACb,CAAC,CAAC;MAEF,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAwE,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;QACpB;QACAC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;QACvCD,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;QAC9CD,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;;QAE1C;QACAC,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,eAAe,EAAE;UACpDC,MAAM,EAAE;YACNC,MAAM,EAAExF,IAAI,CAACsB,GAAG;YAChBmE,QAAQ,EAAE,EAAAX,gBAAA,GAAA1E,QAAQ,CAACsF,MAAM,cAAAZ,gBAAA,uBAAfA,gBAAA,CAAiBa,SAAS,KAAI,CAAC;YACzCC,UAAU,EAAE,EAAAb,iBAAA,GAAA3E,QAAQ,CAACsF,MAAM,cAAAX,iBAAA,uBAAfA,iBAAA,CAAiBa,UAAU,KAAI,CAAC;YAC5CC,OAAO,EAAE,EAAAb,iBAAA,GAAA5E,QAAQ,CAACsF,MAAM,cAAAV,iBAAA,uBAAfA,iBAAA,CAAiBa,OAAO,KAAI,KAAK;YAC1CC,QAAQ,EAAE,EAAAb,iBAAA,GAAA7E,QAAQ,CAACsF,MAAM,cAAAT,iBAAA,uBAAfA,iBAAA,CAAiBa,QAAQ,KAAI9F,IAAI,CAAC+F;UAC9C;QACF,CAAC,CAAC,CAAC;;QAEH;QACAnF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAET,QAAQ,CAAC;QACrDQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAET,QAAQ,CAACsF,MAAM,CAAC;QAEpD,MAAMM,YAAY,GAAG;UACnB,GAAGrB,UAAU;UACbe,MAAM,EAAEtF,QAAQ,CAACsF;QACnB,CAAC;QAED9E,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmF,YAAY,CAAC;QACrDlG,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAAEI,KAAK,EAAE;YAAEqD,MAAM,EAAE0C;UAAa;QAAE,CAAC,CAAC;MACrE,CAAC,MAAM;QACLnI,OAAO,CAACyE,KAAK,CAAClC,QAAQ,CAACvC,OAAO,CAAC;QAC/B+C,OAAO,CAAC0B,KAAK,CAAC,2BAA2B,EAAElC,QAAQ,CAACvC,OAAO,CAAC;MAC9D;MACAkC,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOsE,KAAK,EAAE;MACdvC,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACyE,KAAK,CAACA,KAAK,CAACzE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACgB,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEkB,EAAE,EAAEG,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExE,MAAMkG,UAAU,GAAGzI,WAAW,CAAC,MAAM;IACnC,MAAM0I,YAAY,GAAG,CAAC,CAAAvH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwD,QAAQ,KAAI,CAAC,IAAI,EAAE;IACnD/C,cAAc,CAAC8G,YAAY,CAAC;IAC5BxG,YAAY,CAACuE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAExB,MAAMiC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtChH,cAAc,CAAEiH,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACL/G,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAAC2G,aAAa,CAAC;EAC9B,CAAC,EAAE,CAACxH,QAAQ,CAAC,CAAC;EAEdpB,SAAS,CAAC,MAAM;IACd,IAAI8B,MAAM,IAAIE,UAAU,EAAE;MACxB+G,aAAa,CAAC/G,UAAU,CAAC;MACzBC,aAAa,CAAC,IAAI,CAAC;MACnBkD,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACrD,MAAM,EAAEE,UAAU,EAAEmD,eAAe,CAAC,CAAC;EAEzCnF,SAAS,CAAC,MAAM;IACd,IAAIsC,EAAE,EAAE;MACNK,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3C,SAAS,CAAC,MAAM;IACd,IAAIsB,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;MACxBL,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDjB,cAAc,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAACxD,SAAS,CAACoC,MAAM,CAAC,CAAC;EAEtB1D,SAAS,CAAC,MAAM;IACdgJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENpJ,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,IAAIE,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;MACpCgF,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACtH,QAAQ,EAAEE,SAAS,CAAC,CAAC;EAEzBtB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIgC,UAAU,EAAE;QACd+G,aAAa,CAAC/G,UAAU,CAAC;QACzBC,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC;EACH,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAI,CAACZ,QAAQ,IAAIE,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE3C,OAAA;MAAKsI,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGvI,OAAA;QAAKsI,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvI,OAAA;UAAKsI,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F3I,OAAA;UAAGsI,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9B,CAAClI,QAAQ,GAAG,sBAAsB,GAAG;QAAsB;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACJ3I,OAAA;UAAGsI,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,kBACxB,EAAClI,QAAQ,GAAG,QAAQ,GAAG,MAAM,EAAC,cAAY,EAACE,SAAS,CAACoC,MAAM;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAIA;EACA,IAAIlI,qBAAqB,IAAIF,SAAS,CAACoC,MAAM,EAAE;IAC7CjC,wBAAwB,CAAC,CAAC,CAAC;IAC3B,oBACEV,OAAA;MAAKsI,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGvI,OAAA;QAAKsI,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvI,OAAA;UAAKsI,SAAS,EAAC;QAAsE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5F3I,OAAA;UAAGsI,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,eAAe,GAAGrI,SAAS,CAACE,qBAAqB,CAAC;EACxD,IAAI,CAACmI,eAAe,EAAE;IACpB,oBACE5I,OAAA;MAAKsI,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGvI,OAAA;QAAKsI,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC9EvI,OAAA;UAAKsI,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD3I,OAAA;UAAIsI,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF3I,OAAA;UAAGsI,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/E3I,OAAA;UAAKsI,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvI,OAAA;YACE6I,OAAO,EAAEA,CAAA,KAAMnI,wBAAwB,CAAC,CAAC,CAAE;YAC3C4H,SAAS,EAAC,sGAAsG;YAAAC,QAAA,EACjH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3I,OAAA;YACE6I,OAAO,EAAEA,CAAA,KAAMrH,QAAQ,CAAC,YAAY,CAAE;YACtC8G,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACArG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;IAC7ClC,QAAQ,EAAEA,QAAQ,GAAG;MAAEoC,IAAI,EAAEpC,QAAQ,CAACoC,IAAI;MAAElB,EAAE,EAAElB,QAAQ,CAAC2C;IAAI,CAAC,GAAG,IAAI;IACrE8F,cAAc,EAAEvI,SAAS,CAACoC,MAAM;IAChClC,qBAAqB;IACrBmI,eAAe,EAAErI,SAAS,CAACE,qBAAqB,CAAC,GAAG;MAClDc,EAAE,EAAEhB,SAAS,CAACE,qBAAqB,CAAC,CAACuC,GAAG;MACxCP,IAAI,EAAE,EAAAtC,qBAAA,GAAAI,SAAS,CAACE,qBAAqB,CAAC,CAACgC,IAAI,cAAAtC,qBAAA,uBAArCA,qBAAA,CAAuC4I,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK;MACrE7F,IAAI,EAAE3C,SAAS,CAACE,qBAAqB,CAAC,CAACyC,IAAI;MAC3CE,UAAU,EAAE7C,SAAS,CAACE,qBAAqB,CAAC,CAAC4C,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAChD,SAAS,CAACE,qBAAqB,CAAC,CAAC4C,OAAO,CAAC,CAACV,MAAM,GAAG;IACxH,CAAC,GAAG,IAAI;IACRqG,QAAQ,EAAEnI;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMoI,cAAc,GAAG1I,SAAS,CAACE,qBAAqB,CAAC;EACvD,MAAMyI,QAAQ,GAAI,QAAO7I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2C,GAAI,IAAGvC,qBAAsB,IAAGY,WAAY,IAAGd,SAAS,CAACoC,MAAO,EAAC;EAEpG,oBACE3C,OAAA;IAAoBsI,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAE3FvI,OAAA;MAAKsI,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzEvI,OAAA;QAAIsI,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnE3I,OAAA;QAAAuI,QAAA,gBAAGvI,OAAA;UAAAuI,QAAA,EAAQ;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACpI,SAAS,CAACoC,MAAM;MAAA;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5D3I,OAAA;QAAAuI,QAAA,gBAAGvI,OAAA;UAAAuI,QAAA,EAAQ;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAClI,qBAAqB;MAAA;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvE3I,OAAA;QAAAuI,QAAA,gBAAGvI,OAAA;UAAAuI,QAAA,EAAQ;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACM,cAAc,GAAG,KAAK,GAAG,IAAI;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChF3I,OAAA;QAAAuI,QAAA,gBAAGvI,OAAA;UAAAuI,QAAA,EAAQ;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACtH,WAAW;MAAA;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzD3I,OAAA;QAAAuI,QAAA,gBAAGvI,OAAA;UAAAuI,QAAA,EAAQ;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACO,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC5CM,cAAc,iBACbjJ,OAAA;QAAKsI,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBvI,OAAA;UAAAuI,QAAA,gBAAGvI,OAAA;YAAAuI,QAAA,EAAQ;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACM,cAAc,CAACjG,GAAG;QAAA;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzD3I,OAAA;UAAAuI,QAAA,gBAAGvI,OAAA;YAAAuI,QAAA,EAAQ;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,GAAAvI,oBAAA,GAAC6I,cAAc,CAACxG,IAAI,cAAArC,oBAAA,uBAAnBA,oBAAA,CAAqB2I,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLM,cAAc,gBACbjJ,OAAA;MAAKsI,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDvI,OAAA;QAAIsI,SAAS,EAAC,sCAAsC;QAAAC,QAAA,GAAC,WAC1C,EAAC9H,qBAAqB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACoC,MAAM;MAAA;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAEL3I,OAAA;QAAKsI,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EACxDU,cAAc,CAACxG,IAAI,IAAI;MAA6B;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,EAGLM,cAAc,CAAC5F,OAAO,iBACrBrD,OAAA;QAAKsI,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBjF,MAAM,CAAC6F,OAAO,CAACF,cAAc,CAAC5F,OAAO,CAAC,CAACP,GAAG,CAAC,CAAC,CAACsG,GAAG,EAAEC,KAAK,CAAC,kBACvDrJ,OAAA;UAEEsI,SAAS,EAAC,oDAAoD;UAC9DO,OAAO,EAAEA,CAAA,KAAMjI,kBAAkB,CAAC;YAAC,GAAGD,eAAe;YAAE,CAACF,qBAAqB,GAAG2I;UAAG,CAAC,CAAE;UACtFE,KAAK,EAAE;YACLC,eAAe,EAAE5I,eAAe,CAACF,qBAAqB,CAAC,KAAK2I,GAAG,GAAG,SAAS,GAAG,OAAO;YACrFI,WAAW,EAAE7I,eAAe,CAACF,qBAAqB,CAAC,KAAK2I,GAAG,GAAG,SAAS,GAAG;UAC5E,CAAE;UAAAb,QAAA,gBAEFvI,OAAA;YAAAuI,QAAA,GAASa,GAAG,EAAC,GAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACU,KAAK;QAAA,GARzBD,GAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASL,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGD3I,OAAA;QAAKsI,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCvI,OAAA;UACE6I,OAAO,EAAEA,CAAA,KAAMnI,wBAAwB,CAAC+E,IAAI,CAACgE,GAAG,CAAC,CAAC,EAAEhJ,qBAAqB,GAAG,CAAC,CAAC,CAAE;UAChFiJ,QAAQ,EAAEjJ,qBAAqB,KAAK,CAAE;UACtC6H,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EACzE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3I,OAAA;UACE6I,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIpI,qBAAqB,KAAKF,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;cAClDyB,eAAe,CAAC,CAAC;YACnB,CAAC,MAAM;cACL1D,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;YACrD;UACF,CAAE;UACF6H,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAEnD9H,qBAAqB,KAAKF,SAAS,CAACoC,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG;QAAM;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN3I,OAAA;MAAKsI,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAC1EvI,OAAA;QAAIsI,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjE3I,OAAA;QAAGsI,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,WAAS,EAAC9H,qBAAqB,GAAG,CAAC,EAAC,uBAAqB;MAAA;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CACN;EAAA,GA3EOO,QAAQ;IAAAV,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA4Eb,CAAC;AAEV,CAAC;AAACzI,EAAA,CAzcID,QAAQ;EAAA,QAWGd,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAqK,EAAA,GAdxB1J,QAAQ;AA2cd,eAAeA,QAAQ;AAAC,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}