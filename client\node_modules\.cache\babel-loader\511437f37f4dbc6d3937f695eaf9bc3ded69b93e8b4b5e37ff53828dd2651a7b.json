{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n// Removed CSS import to avoid overflow conflicts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data to prevent object rendering errors (always call this)\n  const questionData = question ? extractQuestionData(question) : null;\n\n  // Debug logging\n  console.log('🎯 QuizRenderer Debug:', {\n    questionIndex,\n    totalQuestions,\n    question: question,\n    questionData: questionData,\n    selectedAnswer,\n    timeLeft\n  });\n\n  // Make question data available globally for debugging\n  window.lastQuestionData = questionData;\n  window.lastQuestion = question;\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 QuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Simplified safety check - just render a simple fallback if no question\n  if (!question || !questionData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-blue-500 text-4xl mb-4\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Loading Question...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please wait while the question loads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full h-1 bg-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-500\",\n        style: {\n          width: `${progressPercentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b border-gray-200 px-4 py-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-sm font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold text-gray-900\",\n            children: safeString(examTitle, 'Quiz')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\",\n          children: [questionIndex + 1, \" of \", totalQuestions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-6 mb-6 border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6 leading-relaxed\",\n            style: {\n              minHeight: '60px',\n              backgroundColor: '#f9fafb',\n              padding: '16px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              display: 'block',\n              visibility: 'visible',\n              opacity: 1\n            },\n            children: [questionData.name || 'Question text loading...', /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#666',\n                marginTop: '8px'\n              },\n              children: [\"Debug: renderKey=\", renderKey, \", hasQuestion=\", !!question, \", hasQuestionData=\", !!questionData]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full h-auto max-h-64 rounded-lg shadow-md object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: questionData.options && Object.keys(questionData.options).length > 0 ? Object.entries(questionData.options).map(([key, value], index) => {\n              const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n              const label = optionLabels[index] || key;\n              const isSelected = currentAnswer === key;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleAnswerSelect(key),\n                className: `w-full text-left p-4 rounded-xl border-2 transition-all duration-300 cursor-pointer min-h-[64px] ${isSelected ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-600'}`,\n                    children: label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex-1 font-medium\",\n                    children: value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 25\n                  }, this), isSelected && /*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 21\n              }, this);\n            }) :\n            /*#__PURE__*/\n            // Fill in the blank question\n            _jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: currentAnswer,\n                onChange: e => handleAnswerSelect(e.target.value),\n                placeholder: \"Type your answer here...\",\n                className: \"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all text-lg font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t border-gray-200 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 py-2 rounded-lg text-sm border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Please select an answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-lg text-sm border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Answer selected\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, `quiz-renderer-${renderKey}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"PH6vAwz56JboJBKDi/YYMWcz/c4=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionData", "console", "log", "window", "lastQuestionData", "lastQuestion", "name", "prev", "handleAnswerSelect", "answer", "progressPercentage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "minHeight", "backgroundColor", "padding", "border", "borderRadius", "display", "visibility", "opacity", "fontSize", "color", "marginTop", "image", "src", "alt", "options", "Object", "keys", "length", "entries", "map", "key", "value", "index", "optionLabels", "label", "isSelected", "onClick", "type", "onChange", "e", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n// Removed CSS import to avoid overflow conflicts\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data to prevent object rendering errors (always call this)\n  const questionData = question ? extractQuestionData(question) : null;\n\n  // Debug logging\n  console.log('🎯 QuizRenderer Debug:', {\n    questionIndex,\n    totalQuestions,\n    question: question,\n    questionData: questionData,\n    selectedAnswer,\n    timeLeft\n  });\n\n  // Make question data available globally for debugging\n  window.lastQuestionData = questionData;\n  window.lastQuestion = question;\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 QuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Simplified safety check - just render a simple fallback if no question\n  if (!question || !questionData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-blue-500 text-4xl mb-4\">⏳</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Loading Question...</h3>\n          <p className=\"text-gray-600\">Please wait while the question loads.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div key={`quiz-renderer-${renderKey}`} className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-auto\">\n      {/* Progress Bar */}\n      <div className=\"w-full h-1 bg-gray-200\">\n        <div\n          className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-500\"\n          style={{ width: `${progressPercentage}%` }}\n        />\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-white shadow-lg border-b border-gray-200 px-4 py-3\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          {/* Timer */}\n          <div className={`flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-sm font-bold transition-all ${\n            isTimeWarning\n              ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n              : 'bg-blue-100 text-blue-700 border border-blue-300'\n          }`}>\n            <TbClock className=\"w-4 h-4\" />\n            <span>{formatTime(timeLeft)}</span>\n          </div>\n\n          {/* Quiz Title */}\n          <div className=\"text-center\">\n            <h1 className=\"text-lg font-bold text-gray-900\">{safeString(examTitle, 'Quiz')}</h1>\n          </div>\n\n          {/* Question Counter */}\n          <div className=\"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\">\n            {questionIndex + 1} of {totalQuestions}\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-6 mb-6 border border-gray-100\">\n\n            {/* Question Number Badge */}\n            <div className=\"mb-6\">\n              <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg\">\n                <span>Question {questionIndex + 1} of {totalQuestions}</span>\n              </div>\n            </div>\n\n            {/* Question Text */}\n            <div\n              className=\"text-xl font-semibold text-gray-900 mb-6 leading-relaxed\"\n              style={{\n                minHeight: '60px',\n                backgroundColor: '#f9fafb',\n                padding: '16px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '8px',\n                display: 'block',\n                visibility: 'visible',\n                opacity: 1\n              }}\n            >\n              {questionData.name || 'Question text loading...'}\n              {/* Debug info */}\n              <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>\n                Debug: renderKey={renderKey}, hasQuestion={!!question}, hasQuestionData={!!questionData}\n              </div>\n            </div>\n\n            {/* Question Image */}\n            {questionData.image && (\n              <div className=\"mb-6 text-center\">\n                <div className=\"inline-block bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm\">\n                  <img\n                    src={questionData.image}\n                    alt=\"Question\"\n                    className=\"max-w-full h-auto max-h-64 rounded-lg shadow-md object-contain\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Answer Options */}\n            <div className=\"space-y-3\">\n              {questionData.options && Object.keys(questionData.options).length > 0 ? (\n                Object.entries(questionData.options).map(([key, value], index) => {\n                  const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                  const label = optionLabels[index] || key;\n                  const isSelected = currentAnswer === key;\n\n                  return (\n                    <div\n                      key={key}\n                      onClick={() => handleAnswerSelect(key)}\n                      className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 cursor-pointer min-h-[64px] ${\n                        isSelected\n                          ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300'\n                          : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'\n                      }`}\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n                          isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-600'\n                        }`}>\n                          {label}\n                        </div>\n                        <span className=\"flex-1 font-medium\">{value}</span>\n                        {isSelected && <TbCheck className=\"w-5 h-5\" />}\n                      </div>\n                    </div>\n                  );\n                })\n              ) : (\n                // Fill in the blank question\n                <div className=\"space-y-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-lg\">✏️</span>\n                      <span>Your Answer:</span>\n                    </div>\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={currentAnswer}\n                    onChange={(e) => handleAnswerSelect(e.target.value)}\n                    placeholder=\"Type your answer here...\"\n                    className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all text-lg font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg\"\n                  />\n                </div>\n              )}\n            </div>\n\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"bg-white border-t border-gray-200 shadow-lg\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between gap-4\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              <span>Previous</span>\n            </button>\n\n            {/* Answer Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 py-2 rounded-lg text-sm border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Please select an answer</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-lg text-sm border border-green-200\">\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Answer selected</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-5 h-5\" />\n                  <span>Submit Quiz</span>\n                </>\n              ) : (\n                <>\n                  <span>Next</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AACpF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAACkB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAMgC,YAAY,GAAGjB,QAAQ,GAAGR,mBAAmB,CAACQ,QAAQ,CAAC,GAAG,IAAI;;EAEpE;EACAkB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;IACpClB,aAAa;IACbC,cAAc;IACdF,QAAQ,EAAEA,QAAQ;IAClBiB,YAAY,EAAEA,YAAY;IAC1Bd,cAAc;IACdE;EACF,CAAC,CAAC;;EAEF;EACAe,MAAM,CAACC,gBAAgB,GAAGJ,YAAY;EACtCG,MAAM,CAACE,YAAY,GAAGtB,QAAQ;EAE9Bd,SAAS,CAAC,MAAM;IACd0B,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACAf,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,IAAIiB,YAAY,IAAIA,YAAY,CAACM,IAAI,EAAE;MACjDL,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEH,YAAY,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACxB,QAAQ,EAAEiB,YAAY,CAAC,CAAC;EAE5B,MAAMQ,kBAAkB,GAAIC,MAAM,IAAK;IACrCd,gBAAgB,CAACc,MAAM,CAAC;IACxBZ,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACsB,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAAC1B,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACiB,YAAY,EAAE;IAC9B,oBACErB,OAAA;MAAKgC,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGjC,OAAA;QAAKgC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DjC,OAAA;UAAKgC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDrC,OAAA;UAAIgC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFrC,OAAA;UAAGgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAwCgC,SAAS,EAAC,mFAAmF;IAAAC,QAAA,gBAEnIjC,OAAA;MAAKgC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCjC,OAAA;QACEgC,SAAS,EAAC,iFAAiF;QAC3FM,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAER,kBAAmB;QAAG;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpEjC,OAAA;QAAKgC,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElEjC,OAAA;UAAKgC,SAAS,EAAG,2FACfnB,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;UAAAoB,QAAA,gBACDjC,OAAA,CAACR,OAAO;YAACwC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BrC,OAAA;YAAAiC,QAAA,EAAOnC,UAAU,CAACW,QAAQ;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAGNrC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BjC,OAAA;YAAIgC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEpC,UAAU,CAACe,SAAS,EAAE,MAAM;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAGNrC,OAAA;UAAKgC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,GAClF5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFjC,OAAA;QAAKgC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CjC,OAAA;UAAKgC,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAG7EjC,OAAA;YAAKgC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBjC,OAAA;cAAKgC,SAAS,EAAC,+IAA+I;cAAAC,QAAA,eAC5JjC,OAAA;gBAAAiC,QAAA,GAAM,WAAS,EAAC5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrC,OAAA;YACEgC,SAAS,EAAC,0DAA0D;YACpEM,KAAK,EAAE;cACLE,SAAS,EAAE,MAAM;cACjBC,eAAe,EAAE,SAAS;cAC1BC,OAAO,EAAE,MAAM;cACfC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,OAAO;cAChBC,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE;YACX,CAAE;YAAAd,QAAA,GAEDZ,YAAY,CAACM,IAAI,IAAI,0BAA0B,eAEhD3B,OAAA;cAAKsC,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAM,CAAE;cAAAjB,QAAA,GAAC,mBAChD,EAACd,SAAS,EAAC,gBAAc,EAAC,CAAC,CAACf,QAAQ,EAAC,oBAAkB,EAAC,CAAC,CAACiB,YAAY;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLhB,YAAY,CAAC8B,KAAK,iBACjBnD,OAAA;YAAKgC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BjC,OAAA;cAAKgC,SAAS,EAAC,yEAAyE;cAAAC,QAAA,eACtFjC,OAAA;gBACEoD,GAAG,EAAE/B,YAAY,CAAC8B,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACdrB,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDrC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBZ,YAAY,CAACiC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACnC,YAAY,CAACiC,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GACnEF,MAAM,CAACG,OAAO,CAACrC,YAAY,CAACiC,OAAO,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;cAChE,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACnD,MAAMC,KAAK,GAAGD,YAAY,CAACD,KAAK,CAAC,IAAIF,GAAG;cACxC,MAAMK,UAAU,GAAGlD,aAAa,KAAK6C,GAAG;cAExC,oBACE5D,OAAA;gBAEEkE,OAAO,EAAEA,CAAA,KAAMrC,kBAAkB,CAAC+B,GAAG,CAAE;gBACvC5B,SAAS,EAAG,oGACViC,UAAU,GACN,uEAAuE,GACvE,+FACL,EAAE;gBAAAhC,QAAA,eAEHjC,OAAA;kBAAKgC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCjC,OAAA;oBAAKgC,SAAS,EAAG,2EACfiC,UAAU,GAAG,wBAAwB,GAAG,2BACzC,EAAE;oBAAAhC,QAAA,EACA+B;kBAAK;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrC,OAAA;oBAAMgC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE4B;kBAAK;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAClD4B,UAAU,iBAAIjE,OAAA,CAACL,OAAO;oBAACqC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC,GAhBDuB,GAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBL,CAAC;YAEV,CAAC,CAAC;YAAA;YAEF;YACArC,OAAA;cAAKgC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjC,OAAA;gBAAOgC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,eAC7DjC,OAAA;kBAAKgC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCjC,OAAA;oBAAMgC,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCrC,OAAA;oBAAAiC,QAAA,EAAM;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRrC,OAAA;gBACEmE,IAAI,EAAC,MAAM;gBACXN,KAAK,EAAE9C,aAAc;gBACrBqD,QAAQ,EAAGC,CAAC,IAAKxC,kBAAkB,CAACwC,CAAC,CAACC,MAAM,CAACT,KAAK,CAAE;gBACpDU,WAAW,EAAC,0BAA0B;gBACtCvC,SAAS,EAAC;cAAmM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9M,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DjC,OAAA;QAAKgC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CjC,OAAA;UAAKgC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACtDjC,OAAA;YACEkE,OAAO,EAAEvD,UAAW;YACpB6D,QAAQ,EAAEnE,aAAa,KAAK,CAAE;YAC9B2B,SAAS,EAAG,6EACV3B,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAA4B,QAAA,gBAEHjC,OAAA,CAACP,WAAW;cAACuC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCrC,OAAA;cAAAiC,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGTrC,OAAA;YAAKgC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAAChB,UAAU,gBACVjB,OAAA;cAAKgC,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHjC,OAAA;gBAAAiC,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfrC,OAAA;gBAAAiC,QAAA,EAAM;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,gBAENrC,OAAA;cAAKgC,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHjC,OAAA,CAACL,OAAO;gBAACqC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BrC,OAAA;gBAAAiC,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrC,OAAA;YACEkE,OAAO,EAAExD,MAAO;YAChB8D,QAAQ,EAAE,CAACvD,UAAW;YACtBe,SAAS,EAAG,6EACV,CAACf,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4DAA4D,GAC5D,0DACP,EAAE;YAAA2B,QAAA,EAEF5B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAA+B,QAAA,gBACEjC,OAAA,CAACL,OAAO;gBAACqC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BrC,OAAA;gBAAAiC,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB,CAAC,gBAEHrC,OAAA,CAAAE,SAAA;cAAA+B,QAAA,gBACEjC,OAAA;gBAAAiC,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjBrC,OAAA,CAACN,YAAY;gBAACsC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,GA/LG,iBAAgBlB,SAAU,EAAC;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAgMjC,CAAC;AAEV,CAAC;AAACvB,EAAA,CAtQIX,YAAY;AAAAsE,EAAA,GAAZtE,YAAY;AAwQlB,eAAeA,YAAY;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}