import React, { useState, useEffect } from 'react';
import {
  Tb<PERSON><PERSON>,
  TbArrowLeft,
  TbArrowRight,
  Tb<PERSON>heck,
  TbBrain,
  TbTarget
} from 'react-icons/tb';
import {
  extractQuestionData,
  safeString,
  formatTime
} from '../utils/quizDataUtils';

const ModernQuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  selectedOptions = {},
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  onQuestionNavigate,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);
  const [renderKey, setRenderKey] = useState(0);

  const questionData = question ? extractQuestionData(question) : null;

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  useEffect(() => {
    if (question && questionData && questionData.name) {
      setRenderKey(prev => prev + 1);
    }
  }, [question, questionData]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(!!answer && answer.toString().trim() !== '');
    if (onAnswerChange) {
      onAnswerChange(answer);
    }
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  if (!question || !questionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-blue-500 text-4xl mb-4">⏳</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Loading Question...</h3>
          <p className="text-gray-600 mb-4">Please wait while the question loads.</p>
        </div>
      </div>
    );
  }

  return (
    <div key={`modern-quiz-${renderKey}`} className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="w-full h-1.5 bg-gray-200/60">
            <div
              className="h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <TbBrain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md">
                    {examTitle}
                  </h1>
                  <p className="text-sm text-gray-500 font-medium">
                    Question {questionIndex + 1} of {totalQuestions}
                  </p>
                </div>
              </div>
            </div>
            <div
              className={`flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${
                isTimeWarning
                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200'
                  : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'
              }`}
            >
              <TbClock className={`w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`} />
              <span className="text-sm font-mono tracking-wider">{formatTime(timeLeft)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* You can paste the rest of the content you posted here below this comment */}

    </div>
  );
};

export default ModernQuizRenderer;
