import React, { useState, useEffect } from 'react';
import { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, Tb<PERSON>heck, TbBrain, TbTarget } from 'react-icons/tb';
import { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';

const ModernQuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);
  const [renderKey, setRenderKey] = useState(0);

  // Extract safe question data
  const questionData = question ? extractQuestionData(question) : null;

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  // Force re-render when question changes
  useEffect(() => {
    if (question && questionData && questionData.name) {
      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');
      setRenderKey(prev => prev + 1);
    }
  }, [question, questionData]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Safety check
  if (!question || !questionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-blue-500 text-4xl mb-4">⏳</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Loading Question...</h3>
          <p className="text-gray-600">Please wait while the question loads.</p>
        </div>
      </div>
    );
  }

  return (
    <div key={`modern-quiz-${renderKey}`} className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Modern Sticky Header */}
      <div className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Progress Bar */}
          <div className="w-full h-1.5 bg-gray-200/60">
            <div
              className="h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          {/* Header Content */}
          <div className="flex items-center justify-between py-4">
            {/* Left: Quiz Info */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <TbBrain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md">
                    {examTitle}
                  </h1>
                  <p className="text-sm text-gray-500 font-medium">
                    Question {questionIndex + 1} of {totalQuestions}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Right: Timer */}
            <div
              className={`flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${
                isTimeWarning
                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200'
                  : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'
              }`}
            >
              <TbClock className={`w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`} />
              <span className="text-sm font-mono tracking-wider">{formatTime(timeLeft)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Quiz Content */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500">
            {/* Question Header */}
            <div className="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <span className="text-white font-bold text-xl">{questionIndex + 1}</span>
                  </div>
                  <div>
                    <h2 className="text-white font-bold text-xl sm:text-2xl">Question {questionIndex + 1}</h2>
                    <p className="text-blue-100 text-sm font-medium">of {totalQuestions} questions</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-white/90 text-sm font-medium">Progress</div>
                  <div className="text-white font-bold text-lg">{Math.round(progressPercentage)}%</div>
                </div>
              </div>
            </div>

            {/* Question Content */}
            <div className="p-6 sm:p-8 lg:p-10">
              {/* Question Text */}
              <div className="mb-8">
                <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm">
                  <h3 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed">
                    {questionData.name || 'Loading question...'}
                  </h3>
                </div>
              </div>

              {/* Question Image */}
              {questionData.image && (
                <div className="mb-8 text-center">
                  <div className="inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm">
                    <img
                      src={questionData.image}
                      alt="Question"
                      className="max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain"
                    />
                  </div>
                </div>
              )}

              {/* Answer Options */}
              <div className="space-y-4 mb-8">
                {questionData.options && Object.keys(questionData.options).length > 0 ? (
                  Object.entries(questionData.options).map(([key, value], index) => {
                    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];
                    const label = optionLabels[index] || key;
                    const isSelected = currentAnswer === key;

                    return (
                      <div
                        key={key}
                        onClick={() => handleAnswerSelect(key)}
                        className={`group cursor-pointer transition-all duration-300 ${
                          isSelected
                            ? 'transform scale-[1.02]'
                            : 'hover:transform hover:scale-[1.01]'
                        }`}
                      >
                        <div className={`p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${
                          isSelected
                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200'
                            : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'
                        }`}>
                          <div className="flex items-center gap-4">
                            <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                              isSelected 
                                ? 'bg-white/20 text-white' 
                                : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'
                            }`}>
                              {label}
                            </div>
                            <span className="flex-1 font-medium text-base sm:text-lg leading-relaxed">{value}</span>
                            {isSelected && (
                              <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                                <TbCheck className="w-4 h-4 text-white" />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 text-4xl mb-4">📝</div>
                    <p className="text-gray-500">No answer options available for this question.</p>
                  </div>
                )}
              </div>

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <button
                  onClick={onPrevious}
                  disabled={questionIndex === 0}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 ${
                    questionIndex === 0
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg'
                  }`}
                >
                  <TbArrowLeft className="w-5 h-5" />
                  <span className="hidden sm:inline">Previous</span>
                </button>

                <div className="flex items-center gap-2">
                  {Array.from({ length: totalQuestions }, (_, i) => (
                    <div
                      key={i}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        i === questionIndex
                          ? 'bg-blue-500 scale-125'
                          : i < questionIndex
                          ? 'bg-green-500'
                          : 'bg-gray-300'
                      }`}
                    />
                  ))}
                </div>

                <button
                  onClick={onNext}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
                >
                  <span>{questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'}</span>
                  {questionIndex === totalQuestions - 1 ? (
                    <TbTarget className="w-5 h-5" />
                  ) : (
                    <TbArrowRight className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
          </div>
      </div>
    </div>
  );
};

export default ModernQuizRenderer;
