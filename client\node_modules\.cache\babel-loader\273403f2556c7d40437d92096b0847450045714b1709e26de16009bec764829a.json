{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data\n  const questionData = question ? extractQuestionData(question) : null;\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Safety check\n  if (!question || !questionData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-blue-500 text-4xl mb-4\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Loading Question...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please wait while the question loads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-1.5 bg-gray-200/60\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\",\n            style: {\n              width: `${progressPercentage}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\",\n                  children: examTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 font-medium\",\n                  children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${isTimeWarning ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200' : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: `w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-mono tracking-wider\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.4,\n            ease: \"easeOut\"\n          },\n          className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-xl\",\n                    children: questionIndex + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-white font-bold text-xl sm:text-2xl\",\n                    children: [\"Question \", questionIndex + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-blue-100 text-sm font-medium\",\n                    children: [\"of \", totalQuestions, \" questions\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-white/90 text-sm font-medium\",\n                  children: \"Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-white font-bold text-lg\",\n                  children: [Math.round(progressPercentage), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 sm:p-8 lg:p-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\",\n                  children: questionData.name || 'Loading question...'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), questionData.image && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.95\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.2\n              },\n              className: \"mb-8 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: questionData.image,\n                  alt: \"Question\",\n                  className: \"max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 mb-8\",\n              children: questionData.options && Object.keys(questionData.options).length > 0 ? Object.entries(questionData.options).map(([key, value], index) => {\n                const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                const label = optionLabels[index] || key;\n                const isSelected = currentAnswer === key;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.1 * index\n                  },\n                  onClick: () => handleAnswerSelect(key),\n                  className: `group cursor-pointer transition-all duration-300 ${isSelected ? 'transform scale-[1.02]' : 'hover:transform hover:scale-[1.01]'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${isSelected ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${isSelected ? 'bg-white/20 text-white' : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'}`,\n                        children: label\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex-1 font-medium text-base sm:text-lg leading-relaxed\",\n                        children: value\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 29\n                      }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                        initial: {\n                          scale: 0\n                        },\n                        animate: {\n                          scale: 1\n                        },\n                        className: \"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 205,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-400 text-4xl mb-4\",\n                  children: \"\\uD83D\\uDCDD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500\",\n                  children: \"No answer options available for this question.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between pt-6 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: onPrevious,\n                disabled: questionIndex === 0,\n                className: `flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg'}`,\n                children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: Array.from({\n                  length: totalQuestions\n                }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-3 h-3 rounded-full transition-all duration-300 ${i === questionIndex ? 'bg-blue-500 scale-125' : i < questionIndex ? 'bg-green-500' : 'bg-gray-300'}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: onNext,\n                className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, questionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, `modern-quiz-${renderKey}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernQuizRenderer, \"PH6vAwz56JboJBKDi/YYMWcz/c4=\");\n_c = ModernQuizRenderer;\nexport default ModernQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"ModernQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbBrain", "TbTarget", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "ModernQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionData", "name", "console", "log", "prev", "handleAnswerSelect", "answer", "progressPercentage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "AnimatePresence", "mode", "motion", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "ease", "Math", "round", "image", "scale", "delay", "src", "alt", "options", "Object", "keys", "length", "entries", "map", "key", "value", "index", "optionLabels", "label", "isSelected", "x", "onClick", "button", "whileHover", "whileTap", "disabled", "Array", "from", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, Tb<PERSON>heck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  // Extract safe question data\n  const questionData = question ? extractQuestionData(question) : null;\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Force re-render when question changes\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      console.log('🔄 ModernQuizRenderer: Question data changed, forcing re-render');\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Safety check\n  if (!question || !questionData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-blue-500 text-4xl mb-4\">⏳</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Loading Question...</h3>\n          <p className=\"text-gray-600\">Please wait while the question loads.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div key={`modern-quiz-${renderKey}`} className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Modern Sticky Header */}\n      <div className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Progress Bar */}\n          <div className=\"w-full h-1.5 bg-gray-200/60\">\n            <div\n              className=\"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\"\n              style={{ width: `${progressPercentage}%` }}\n            />\n          </div>\n          \n          {/* Header Content */}\n          <div className=\"flex items-center justify-between py-4\">\n            {/* Left: Quiz Info */}\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\">\n                  <TbBrain className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h1 className=\"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\">\n                    {examTitle}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 font-medium\">\n                    Question {questionIndex + 1} of {totalQuestions}\n                  </p>\n                </div>\n              </div>\n            </div>\n            \n            {/* Right: Timer */}\n            <div\n              className={`flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${\n                isTimeWarning\n                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200'\n                  : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'\n              }`}\n            >\n              <TbClock className={`w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`} />\n              <span className=\"text-sm font-mono tracking-wider\">{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Quiz Content */}\n      <div className=\"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={questionIndex}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.4, ease: \"easeOut\" }}\n            className=\"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden\"\n          >\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\">\n                    <span className=\"text-white font-bold text-xl\">{questionIndex + 1}</span>\n                  </div>\n                  <div>\n                    <h2 className=\"text-white font-bold text-xl sm:text-2xl\">Question {questionIndex + 1}</h2>\n                    <p className=\"text-blue-100 text-sm font-medium\">of {totalQuestions} questions</p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-white/90 text-sm font-medium\">Progress</div>\n                  <div className=\"text-white font-bold text-lg\">{Math.round(progressPercentage)}%</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Question Content */}\n            <div className=\"p-6 sm:p-8 lg:p-10\">\n              {/* Question Text */}\n              <div className=\"mb-8\">\n                <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\">\n                  <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\">\n                    {questionData.name || 'Loading question...'}\n                  </h3>\n                </div>\n              </div>\n\n              {/* Question Image */}\n              {questionData.image && (\n                <motion.div \n                  initial={{ opacity: 0, scale: 0.95 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.2 }}\n                  className=\"mb-8 text-center\"\n                >\n                  <div className=\"inline-block bg-gray-50 rounded-2xl p-4 border border-gray-200 shadow-sm\">\n                    <img\n                      src={questionData.image}\n                      alt=\"Question\"\n                      className=\"max-w-full h-auto max-h-80 rounded-xl shadow-md object-contain\"\n                    />\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Answer Options */}\n              <div className=\"space-y-4 mb-8\">\n                {questionData.options && Object.keys(questionData.options).length > 0 ? (\n                  Object.entries(questionData.options).map(([key, value], index) => {\n                    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                    const label = optionLabels[index] || key;\n                    const isSelected = currentAnswer === key;\n\n                    return (\n                      <motion.div\n                        key={key}\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: 0.1 * index }}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`group cursor-pointer transition-all duration-300 ${\n                          isSelected\n                            ? 'transform scale-[1.02]'\n                            : 'hover:transform hover:scale-[1.01]'\n                        }`}\n                      >\n                        <div className={`p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${\n                          isSelected\n                            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200'\n                            : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'\n                        }`}>\n                          <div className=\"flex items-center gap-4\">\n                            <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${\n                              isSelected \n                                ? 'bg-white/20 text-white' \n                                : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'\n                            }`}>\n                              {label}\n                            </div>\n                            <span className=\"flex-1 font-medium text-base sm:text-lg leading-relaxed\">{value}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0 }}\n                                animate={{ scale: 1 }}\n                                className=\"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\"\n                              >\n                                <TbCheck className=\"w-4 h-4 text-white\" />\n                              </motion.div>\n                            )}\n                          </div>\n                        </div>\n                      </motion.div>\n                    );\n                  })\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <div className=\"text-gray-400 text-4xl mb-4\">📝</div>\n                    <p className=\"text-gray-500\">No answer options available for this question.</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Navigation Buttons */}\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={onPrevious}\n                  disabled={questionIndex === 0}\n                  className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                    questionIndex === 0\n                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300 shadow-md hover:shadow-lg'\n                  }`}\n                >\n                  <TbArrowLeft className=\"w-5 h-5\" />\n                  <span className=\"hidden sm:inline\">Previous</span>\n                </motion.button>\n\n                <div className=\"flex items-center gap-2\">\n                  {Array.from({ length: totalQuestions }, (_, i) => (\n                    <div\n                      key={i}\n                      className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                        i === questionIndex\n                          ? 'bg-blue-500 scale-125'\n                          : i < questionIndex\n                          ? 'bg-green-500'\n                          : 'bg-gray-300'\n                      }`}\n                    />\n                  ))}\n                </div>\n\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={onNext}\n                  className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\n                >\n                  <span>{questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'}</span>\n                  {questionIndex === totalQuestions - 1 ? (\n                    <TbTarget className=\"w-5 h-5\" />\n                  ) : (\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  )}\n                </motion.button>\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/F,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAACiB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAM+B,YAAY,GAAGjB,QAAQ,GAAGN,mBAAmB,CAACM,QAAQ,CAAC,GAAG,IAAI;EAEpEb,SAAS,CAAC,MAAM;IACdyB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACAd,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,IAAIiB,YAAY,IAAIA,YAAY,CAACC,IAAI,EAAE;MACjDC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9EJ,YAAY,CAACK,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACrB,QAAQ,EAAEiB,YAAY,CAAC,CAAC;EAE5B,MAAMK,kBAAkB,GAAIC,MAAM,IAAK;IACrCX,gBAAgB,CAACW,MAAM,CAAC;IACxBT,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACmB,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACvB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACiB,YAAY,EAAE;IAC9B,oBACEnB,OAAA;MAAK2B,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG5B,OAAA;QAAK2B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D5B,OAAA;UAAK2B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDhC,OAAA;UAAI2B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFhC,OAAA;UAAG2B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAsC2B,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAEtH5B,OAAA;MAAK2B,SAAS,EAAC,sFAAsF;MAAAC,QAAA,eACnG5B,OAAA;QAAK2B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD5B,OAAA;UAAK2B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C5B,OAAA;YACE2B,SAAS,EAAC,sHAAsH;YAChIM,KAAK,EAAE;cAAEC,KAAK,EAAG,GAAER,kBAAmB;YAAG;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhC,OAAA;UAAK2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD5B,OAAA;YAAK2B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtC5B,OAAA;cAAK2B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC5B,OAAA;gBAAK2B,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAC3H5B,OAAA,CAACN,OAAO;kBAACiC,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNhC,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAI2B,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,EACrFlB;gBAAS;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLhC,OAAA;kBAAG2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,WACtC,EAACzB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YACE2B,SAAS,EAAG,wFACVhB,aAAa,GACT,+EAA+E,GAC/E,uFACL,EAAE;YAAAiB,QAAA,gBAEH5B,OAAA,CAACV,OAAO;cAACqC,SAAS,EAAG,WAAUhB,aAAa,GAAG,eAAe,GAAG,EAAG;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzEhC,OAAA;cAAM2B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAE9B,UAAU,CAACS,QAAQ;YAAC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D5B,OAAA,CAACmC,eAAe;QAACC,IAAI,EAAC,MAAM;QAAAR,QAAA,eAC1B5B,OAAA,CAACqC,MAAM,CAACC,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/CnB,SAAS,EAAC,4FAA4F;UAAAC,QAAA,gBAGtG5B,OAAA;YAAK2B,SAAS,EAAC,+EAA+E;YAAAC,QAAA,eAC5F5B,OAAA;cAAK2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5B,OAAA;gBAAK2B,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC5B,OAAA;kBAAK2B,SAAS,EAAC,qFAAqF;kBAAAC,QAAA,eAClG5B,OAAA;oBAAM2B,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAEzB,aAAa,GAAG;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNhC,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAI2B,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,GAAC,WAAS,EAACzB,aAAa,GAAG,CAAC;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1FhC,OAAA;oBAAG2B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,KAAG,EAACxB,cAAc,EAAC,YAAU;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhC,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAK2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjEhC,OAAA;kBAAK2B,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GAAEmB,IAAI,CAACC,KAAK,CAACtB,kBAAkB,CAAC,EAAC,GAAC;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAK2B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAEjC5B,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5B,OAAA;gBAAK2B,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,eAClH5B,OAAA;kBAAI2B,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EACxFT,YAAY,CAACC,IAAI,IAAI;gBAAqB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLb,YAAY,CAAC8B,KAAK,iBACjBjD,OAAA,CAACqC,MAAM,CAACC,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,KAAK,EAAE;cAAK,CAAE;cACrCR,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEU,KAAK,EAAE;cAAE,CAAE;cAClCN,UAAU,EAAE;gBAAEO,KAAK,EAAE;cAAI,CAAE;cAC3BxB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAE5B5B,OAAA;gBAAK2B,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF5B,OAAA;kBACEoD,GAAG,EAAEjC,YAAY,CAAC8B,KAAM;kBACxBI,GAAG,EAAC,UAAU;kBACd1B,SAAS,EAAC;gBAAgE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDhC,OAAA;cAAK2B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BT,YAAY,CAACmC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACrC,YAAY,CAACmC,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GACnEF,MAAM,CAACG,OAAO,CAACvC,YAAY,CAACmC,OAAO,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;gBAChE,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBACnD,MAAMC,KAAK,GAAGD,YAAY,CAACD,KAAK,CAAC,IAAIF,GAAG;gBACxC,MAAMK,UAAU,GAAGpD,aAAa,KAAK+C,GAAG;gBAExC,oBACE5D,OAAA,CAACqC,MAAM,CAACC,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAE0B,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCxB,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAE0B,CAAC,EAAE;kBAAE,CAAE;kBAC9BtB,UAAU,EAAE;oBAAEO,KAAK,EAAE,GAAG,GAAGW;kBAAM,CAAE;kBACnCK,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAACoC,GAAG,CAAE;kBACvCjC,SAAS,EAAG,oDACVsC,UAAU,GACN,wBAAwB,GACxB,oCACL,EAAE;kBAAArC,QAAA,eAEH5B,OAAA;oBAAK2B,SAAS,EAAG,+DACfsC,UAAU,GACN,mGAAmG,GACnG,yGACL,EAAE;oBAAArC,QAAA,eACD5B,OAAA;sBAAK2B,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACtC5B,OAAA;wBAAK2B,SAAS,EAAG,uGACfsC,UAAU,GACN,wBAAwB,GACxB,mDACL,EAAE;wBAAArC,QAAA,EACAoC;sBAAK;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNhC,OAAA;wBAAM2B,SAAS,EAAC,yDAAyD;wBAAAC,QAAA,EAAEiC;sBAAK;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACvFiC,UAAU,iBACTjE,OAAA,CAACqC,MAAM,CAACC,GAAG;wBACTC,OAAO,EAAE;0BAAEW,KAAK,EAAE;wBAAE,CAAE;wBACtBR,OAAO,EAAE;0BAAEQ,KAAK,EAAE;wBAAE,CAAE;wBACtBvB,SAAS,EAAC,mEAAmE;wBAAAC,QAAA,eAE7E5B,OAAA,CAACP,OAAO;0BAACkC,SAAS,EAAC;wBAAoB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAnCD4B,GAAG;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoCE,CAAC;cAEjB,CAAC,CAAC,gBAEFhC,OAAA;gBAAK2B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B5B,OAAA;kBAAK2B,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDhC,OAAA;kBAAG2B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNhC,OAAA;cAAK2B,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9E5B,OAAA,CAACqC,MAAM,CAAC+B,MAAM;gBACZC,UAAU,EAAE;kBAAEnB,KAAK,EAAE;gBAAK,CAAE;gBAC5BoB,QAAQ,EAAE;kBAAEpB,KAAK,EAAE;gBAAK,CAAE;gBAC1BiB,OAAO,EAAE1D,UAAW;gBACpB8D,QAAQ,EAAEpE,aAAa,KAAK,CAAE;gBAC9BwB,SAAS,EAAG,0FACVxB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,uEACL,EAAE;gBAAAyB,QAAA,gBAEH5B,OAAA,CAACT,WAAW;kBAACoC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnChC,OAAA;kBAAM2B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEhBhC,OAAA;gBAAK2B,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACrC4C,KAAK,CAACC,IAAI,CAAC;kBAAEhB,MAAM,EAAErD;gBAAe,CAAC,EAAE,CAACsE,CAAC,EAAEC,CAAC,kBAC3C3E,OAAA;kBAEE2B,SAAS,EAAG,oDACVgD,CAAC,KAAKxE,aAAa,GACf,uBAAuB,GACvBwE,CAAC,GAAGxE,aAAa,GACjB,cAAc,GACd,aACL;gBAAE,GAPEwE,CAAC;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQP,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhC,OAAA,CAACqC,MAAM,CAAC+B,MAAM;gBACZC,UAAU,EAAE;kBAAEnB,KAAK,EAAE;gBAAK,CAAE;gBAC5BoB,QAAQ,EAAE;kBAAEpB,KAAK,EAAE;gBAAK,CAAE;gBAC1BiB,OAAO,EAAE3D,MAAO;gBAChBmB,SAAS,EAAC,kNAAkN;gBAAAC,QAAA,gBAE5N5B,OAAA;kBAAA4B,QAAA,EAAOzB,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;gBAAM;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC3E7B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA,CAACL,QAAQ;kBAACgC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEhChC,OAAA,CAACR,YAAY;kBAACmC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA9JD7B,aAAa;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+JR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA,GAnNG,eAAcf,SAAU,EAAC;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAoN/B,CAAC;AAEV,CAAC;AAACpB,EAAA,CA5QIX,kBAAkB;AAAA2E,EAAA,GAAlB3E,kBAAkB;AA8QxB,eAAeA,kBAAkB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}