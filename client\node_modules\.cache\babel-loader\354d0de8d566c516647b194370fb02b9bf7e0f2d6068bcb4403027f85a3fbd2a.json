{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport '../pages/user/Quiz/responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n\n  // Debug logging\n  console.log('🎯 QuizRenderer Debug:', {\n    questionIndex,\n    totalQuestions,\n    question: question,\n    questionData: questionData,\n    selectedAnswer,\n    timeLeft\n  });\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Check if question data is valid\n  if (!question) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"No Question Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Question data is missing. Please check the quiz configuration.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Question Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"This question could not be loaded. Please try refreshing the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  }\n  const renderMCQ = () => {\n    if (!questionData.options || Object.keys(questionData.options).length === 0) {\n      console.warn('❌ MCQ question has no options:', questionData);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-red-50 rounded-xl p-6 border border-red-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-4xl mb-2\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-700 mb-2\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: [\"Question ID: \", questionData.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: [\"Type: \", questionData.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-5\",\n      children: Object.entries(questionData.options).map(([key, value], index) => {\n        const optionKey = safeString(key).trim();\n        const optionValue = safeString(value).trim();\n        const label = optionLabels[index] || optionKey;\n        const isSelected = currentAnswer === optionKey;\n\n        // Skip empty options\n        if (!optionValue) return null;\n        return /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => handleAnswerSelect(optionKey),\n          whileHover: {\n            scale: 1.005\n          },\n          whileTap: {\n            scale: 0.995\n          },\n          className: `w-full text-left p-3 sm:p-4 md:p-5 lg:p-6 xl:p-7 rounded-lg sm:rounded-xl md:rounded-2xl border-2 transition-all duration-300 touch-manipulation min-h-[56px] sm:min-h-[64px] md:min-h-[72px] lg:min-h-[80px] xl:min-h-[88px] quiz-option ${isSelected ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300 selected' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md active:bg-blue-100'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 sm:gap-3 md:gap-4 lg:gap-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm md:text-base lg:text-lg transition-all flex-shrink-0 quiz-option-letter ${isSelected ? 'bg-white text-blue-600 shadow-md' : 'bg-blue-100 text-blue-700'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `flex-1 font-medium text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-relaxed break-words quiz-option-text ${isSelected ? 'text-white' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), isSelected && /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4 sm:space-y-5 lg:space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 lg:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg sm:text-xl lg:text-2xl\",\n          children: \"\\u270F\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Your Answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full px-3 sm:px-4 md:px-5 lg:px-6 xl:px-7 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 border-2 border-gray-200 rounded-lg sm:rounded-xl md:rounded-2xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg min-h-[50px] sm:min-h-[56px] md:min-h-[64px] lg:min-h-[72px] xl:min-h-[80px] touch-manipulation quiz-fill-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2\",\n        children: currentAnswer ? /*#__PURE__*/_jsxDEV(TbCheck, {\n          className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n\n  // Fallback render function for debugging\n  const renderFallback = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center bg-orange-50 rounded-xl p-6 border border-orange-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-orange-500 text-4xl mb-2\",\n      children: \"\\uD83D\\uDD27\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-orange-700 mb-2\",\n      children: \"Question rendering issue detected\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-orange-600 text-sm\",\n      children: [\"Question type: \", questionData.type]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-orange-600 text-sm\",\n      children: [\"Has options: \", questionData.options ? 'Yes' : 'No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-orange-600 text-sm\",\n      children: [\"Options count: \", questionData.options ? Object.keys(questionData.options).length : 0]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 p-3 bg-white rounded border text-left text-xs\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Raw question data:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 44\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        children: [JSON.stringify(questionData, null, 2).substring(0, 500), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-container quiz-renderer h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden fixed inset-0 z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"h-full bg-gradient-to-r from-blue-500 to-indigo-600\",\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: `${progressPercentage}%`\n        },\n        transition: {\n          duration: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex sm:hidden items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 text-center px-2\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-base font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold\",\n            children: [questionIndex + 1, \"/\", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex lg:hidden items-center justify-between gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Challenge your brain, beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-base font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\",\n            children: [questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:grid lg:grid-cols-3 items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl xl:text-2xl font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Challenge your brain, beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 xl:px-6 py-2 xl:py-3 rounded-xl font-mono text-lg xl:text-xl font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border-2 border-blue-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5 xl:w-6 xl:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"TIME\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 text-gray-700 px-3 xl:px-4 py-2 xl:py-3 rounded-lg text-sm xl:text-base font-semibold\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 md:py-6 lg:py-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-xl p-3 sm:p-4 md:p-6 lg:p-8 xl:p-10 mb-4 sm:mb-6 lg:mb-8 border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 sm:mb-4 md:mb-6 lg:mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm md:text-base font-semibold shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-semibold text-gray-900 mb-4 sm:mb-5 md:mb-6 lg:mb-8 leading-relaxed quiz-question-text\",\n            children: questionData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 sm:mb-5 md:mb-6 lg:mb-8 text-center quiz-image-container-modern\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3 md:p-4 lg:p-6 border border-gray-200 shadow-sm quiz-image-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full h-auto max-h-40 sm:max-h-48 md:max-h-64 lg:max-h-80 xl:max-h-96 rounded-lg shadow-md object-contain quiz-image-modern\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-options\",\n            children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-xs\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Debug Info:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 47\n              }, this), \"Type: \", questionData.type, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 44\n              }, this), \"Options count: \", questionData.options ? Object.keys(questionData.options).length : 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 103\n              }, this), \"Has options: \", questionData.options ? 'Yes' : 'No', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 69\n              }, this), \"Question ID: \", questionData.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, questionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t border-gray-200 shadow-lg flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex sm:hidden items-center justify-between gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-lg text-xs border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Select answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-lg text-xs border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Answered\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex lg:hidden items-center justify-between gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg text-xs border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Select answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-green-600 bg-green-50 px-3 py-2 rounded-lg text-xs border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Ready\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5 xl:w-6 xl:h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Please select an answer to continue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-green-600 bg-green-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Answer selected - ready to proceed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5 xl:w-6 xl:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5 xl:w-6 xl:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "console", "log", "handleAnswerSelect", "answer", "progressPercentage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "renderMCQ", "options", "Object", "keys", "length", "warn", "id", "type", "optionLabels", "entries", "map", "key", "value", "index", "optionKey", "trim", "optionValue", "label", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "renderFillBlank", "onChange", "e", "target", "placeholder", "renderFallback", "JSON", "stringify", "substring", "div", "initial", "width", "animate", "transition", "duration", "opacity", "x", "image", "src", "alt", "process", "env", "NODE_ENV", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport '../pages/user/Quiz/responsive.css';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n\n  // Debug logging\n  console.log('🎯 QuizRenderer Debug:', {\n    questionIndex,\n    totalQuestions,\n    question: question,\n    questionData: questionData,\n    selectedAnswer,\n    timeLeft\n  });\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Check if question data is valid\n  if (!question) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">No Question Data</h3>\n          <p className=\"text-gray-600\">Question data is missing. Please check the quiz configuration.</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Question Not Available</h3>\n          <p className=\"text-gray-600\">This question could not be loaded. Please try refreshing the page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const renderMCQ = () => {\n    if (!questionData.options || Object.keys(questionData.options).length === 0) {\n      console.warn('❌ MCQ question has no options:', questionData);\n      return (\n        <div className=\"text-center bg-red-50 rounded-xl p-6 border border-red-200\">\n          <div className=\"text-red-500 text-4xl mb-2\">⚠️</div>\n          <p className=\"text-red-700 mb-2\">No options available for this question.</p>\n          <p className=\"text-red-600 text-sm\">Question ID: {questionData.id}</p>\n          <p className=\"text-red-600 text-sm\">Type: {questionData.type}</p>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-5\">\n        {Object.entries(questionData.options).map(([key, value], index) => {\n          const optionKey = safeString(key).trim();\n          const optionValue = safeString(value).trim();\n          const label = optionLabels[index] || optionKey;\n          const isSelected = currentAnswer === optionKey;\n\n          // Skip empty options\n          if (!optionValue) return null;\n\n          return (\n            <motion.button\n              key={optionKey}\n              onClick={() => handleAnswerSelect(optionKey)}\n              whileHover={{ scale: 1.005 }}\n              whileTap={{ scale: 0.995 }}\n              className={`w-full text-left p-3 sm:p-4 md:p-5 lg:p-6 xl:p-7 rounded-lg sm:rounded-xl md:rounded-2xl border-2 transition-all duration-300 touch-manipulation min-h-[56px] sm:min-h-[64px] md:min-h-[72px] lg:min-h-[80px] xl:min-h-[88px] quiz-option ${\n                isSelected\n                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300 selected'\n                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md active:bg-blue-100'\n              }`}\n            >\n              <div className=\"flex items-center gap-2 sm:gap-3 md:gap-4 lg:gap-5\">\n                <div className={`w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm md:text-base lg:text-lg transition-all flex-shrink-0 quiz-option-letter ${\n                  isSelected\n                    ? 'bg-white text-blue-600 shadow-md'\n                    : 'bg-blue-100 text-blue-700'\n                }`}>\n                  {label}\n                </div>\n                <span className={`flex-1 font-medium text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-relaxed break-words quiz-option-text ${\n                  isSelected ? 'text-white' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n                {isSelected && (\n                  <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0\" />\n                )}\n              </div>\n            </motion.button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderFillBlank = () => (\n    <div className=\"space-y-4 sm:space-y-5 lg:space-y-6\">\n      <label className=\"block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3\">\n        <div className=\"flex items-center gap-2 lg:gap-3\">\n          <span className=\"text-lg sm:text-xl lg:text-2xl\">✏️</span>\n          <span>Your Answer:</span>\n        </div>\n      </label>\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full px-3 sm:px-4 md:px-5 lg:px-6 xl:px-7 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 border-2 border-gray-200 rounded-lg sm:rounded-xl md:rounded-2xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg min-h-[50px] sm:min-h-[56px] md:min-h-[64px] lg:min-h-[72px] xl:min-h-[80px] touch-manipulation quiz-fill-input\"\n        />\n        <div className=\"absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2\">\n          {currentAnswer ? (\n            <TbCheck className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500\" />\n          ) : (\n            <div className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full\"></div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  // Fallback render function for debugging\n  const renderFallback = () => (\n    <div className=\"text-center bg-orange-50 rounded-xl p-6 border border-orange-200\">\n      <div className=\"text-orange-500 text-4xl mb-2\">🔧</div>\n      <p className=\"text-orange-700 mb-2\">Question rendering issue detected</p>\n      <p className=\"text-orange-600 text-sm\">Question type: {questionData.type}</p>\n      <p className=\"text-orange-600 text-sm\">Has options: {questionData.options ? 'Yes' : 'No'}</p>\n      <p className=\"text-orange-600 text-sm\">Options count: {questionData.options ? Object.keys(questionData.options).length : 0}</p>\n      <div className=\"mt-4 p-3 bg-white rounded border text-left text-xs\">\n        <strong>Raw question data:</strong><br/>\n        <pre>{JSON.stringify(questionData, null, 2).substring(0, 500)}...</pre>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"quiz-container quiz-renderer h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden fixed inset-0 z-50\">\n      {/* Progress Bar */}\n      <div className=\"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\">\n        <motion.div\n          className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600\"\n          initial={{ width: 0 }}\n          animate={{ width: `${progressPercentage}%` }}\n          transition={{ duration: 0.5 }}\n        />\n      </div>\n\n      {/* Enhanced Header with Better Navigation */}\n      <div className=\"bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4\">\n          {/* Mobile Layout (< 640px) */}\n          <div className=\"flex sm:hidden items-center justify-between\">\n            {/* Timer - Left */}\n            <div className={`flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n                : 'bg-blue-100 text-blue-700 border border-blue-300'\n            }`}>\n              <TbClock className=\"w-4 h-4\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n\n            {/* Quiz Title - Center */}\n            <div className=\"flex-1 text-center px-2\">\n              <h1 className=\"text-base font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n            </div>\n\n            {/* Question Counter - Right */}\n            <div className=\"bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold\">\n              {questionIndex + 1}/{totalQuestions}\n            </div>\n          </div>\n\n          {/* Tablet Layout (640px - 1024px) */}\n          <div className=\"hidden sm:flex lg:hidden items-center justify-between gap-3\">\n            {/* Quiz Title */}\n            <div className=\"flex-1 text-left\">\n              <h1 className=\"text-lg font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n              <p className=\"text-sm text-gray-600\">Challenge your brain, beat the rest</p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-base font-bold transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n                : 'bg-blue-100 text-blue-700 border border-blue-300'\n            }`}>\n              <TbClock className=\"w-4 h-4\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n\n            {/* Question Counter */}\n            <div className=\"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\">\n              {questionIndex + 1} of {totalQuestions}\n            </div>\n          </div>\n\n          {/* Desktop Layout (>= 1024px) */}\n          <div className=\"hidden lg:grid lg:grid-cols-3 items-center gap-4\">\n            {/* Quiz Title */}\n            <div className=\"text-left\">\n              <h1 className=\"text-xl xl:text-2xl font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n              <p className=\"text-sm text-gray-600\">Challenge your brain, beat the rest</p>\n            </div>\n\n            {/* Centered Timer */}\n            <div className=\"flex justify-center\">\n              <div className={`flex items-center gap-2 px-4 xl:px-6 py-2 xl:py-3 rounded-xl font-mono text-lg xl:text-xl font-bold transition-all ${\n                isTimeWarning\n                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse'\n                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'\n              }`}>\n                <TbClock className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n                <span>TIME</span>\n                <span>{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n\n            {/* Question Counter */}\n            <div className=\"flex justify-end\">\n              <div className=\"bg-gray-100 text-gray-700 px-3 xl:px-4 py-2 xl:py-3 rounded-lg text-sm xl:text-base font-semibold\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Scrollable Area */}\n      <div className=\"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"max-w-6xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 md:py-6 lg:py-8\">\n          <motion.div\n            key={questionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-xl p-3 sm:p-4 md:p-6 lg:p-8 xl:p-10 mb-4 sm:mb-6 lg:mb-8 border border-gray-100\"\n\n          >\n            {/* Question Number Badge */}\n            <div className=\"mb-3 sm:mb-4 md:mb-6 lg:mb-8\">\n              <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm md:text-base font-semibold shadow-lg\">\n                <span>Question {questionIndex + 1} of {totalQuestions}</span>\n              </div>\n            </div>\n\n            {/* Question Text */}\n            <div className=\"text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-semibold text-gray-900 mb-4 sm:mb-5 md:mb-6 lg:mb-8 leading-relaxed quiz-question-text\">\n              {questionData.name}\n            </div>\n\n            {/* Question Image */}\n            {questionData.image && (\n              <div className=\"mb-4 sm:mb-5 md:mb-6 lg:mb-8 text-center quiz-image-container-modern\">\n                <div className=\"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3 md:p-4 lg:p-6 border border-gray-200 shadow-sm quiz-image-wrapper\">\n                  <img\n                    src={questionData.image}\n                    alt=\"Question\"\n                    className=\"max-w-full h-auto max-h-40 sm:max-h-48 md:max-h-64 lg:max-h-80 xl:max-h-96 rounded-lg shadow-md object-contain quiz-image-modern\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Question Content */}\n            <div className=\"quiz-options\">\n              {/* Debug information */}\n              {process.env.NODE_ENV === 'development' && (\n                <div className=\"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-xs\">\n                  <strong>Debug Info:</strong><br/>\n                  Type: {questionData.type}<br/>\n                  Options count: {questionData.options ? Object.keys(questionData.options).length : 0}<br/>\n                  Has options: {questionData.options ? 'Yes' : 'No'}<br/>\n                  Question ID: {questionData.id}\n                </div>\n              )}\n\n              {questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Enhanced Bottom Navigation */}\n      <div className=\"bg-white border-t border-gray-200 shadow-lg flex-shrink-0\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          {/* Mobile Navigation */}\n          <div className=\"flex sm:hidden items-center justify-between gap-2\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4\" />\n              <span>Prev</span>\n            </button>\n\n            {/* Center Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-lg text-xs border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Select answer</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-lg text-xs border border-green-200\">\n                  <TbCheck className=\"w-3 h-3\" />\n                  <span>Answered</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Submit</span>\n                </>\n              ) : (\n                <>\n                  <span>Next</span>\n                  <TbArrowRight className=\"w-4 h-4\" />\n                </>\n              )}\n            </button>\n          </div>\n\n          {/* Tablet Navigation (640px - 1024px) */}\n          <div className=\"hidden sm:flex lg:hidden items-center justify-between gap-3\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4\" />\n              <span>Previous</span>\n            </button>\n\n            {/* Center Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-1 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg text-xs border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Select answer</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-1 text-green-600 bg-green-50 px-3 py-2 rounded-lg text-xs border border-green-200\">\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Ready</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Submit</span>\n                </>\n              ) : (\n                <>\n                  <span>Next</span>\n                  <TbArrowRight className=\"w-4 h-4\" />\n                </>\n              )}\n            </button>\n          </div>\n\n          {/* Desktop Navigation (>= 1024px) */}\n          <div className=\"hidden lg:flex items-center justify-between gap-4\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n              <span>Previous</span>\n            </button>\n\n            {/* Center Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Please select an answer to continue</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2 text-green-600 bg-green-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-green-200\">\n                  <TbCheck className=\"w-5 h-5\" />\n                  <span>Answer selected - ready to proceed</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n                  <span>Submit Quiz</span>\n                </>\n              ) : (\n                <>\n                  <span>Next Question</span>\n                  <TbArrowRight className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AACpF,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAACkB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAGvB,mBAAmB,CAACQ,QAAQ,CAAC;;EAElD;EACAgB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;IACpChB,aAAa;IACbC,cAAc;IACdF,QAAQ,EAAEA,QAAQ;IAClBe,YAAY,EAAEA,YAAY;IAC1BZ,cAAc;IACdE;EACF,CAAC,CAAC;EAEFnB,SAAS,CAAC,MAAM;IACd0B,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMiB,kBAAkB,GAAIC,MAAM,IAAK;IACrCP,gBAAgB,CAACO,MAAM,CAAC;IACxBL,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACe,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACnB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEJ,OAAA;MAAKyB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG1B,OAAA;QAAKyB,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC9E1B,OAAA;UAAKyB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD9B,OAAA;UAAIyB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E9B,OAAA;UAAGyB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACX,YAAY,CAACY,IAAI,IAAIZ,YAAY,CAACY,IAAI,KAAK,wBAAwB,EAAE;IACxE,oBACE/B,OAAA;MAAKyB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG1B,OAAA;QAAKyB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D1B,OAAA;UAAKyB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD9B,OAAA;UAAIyB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF9B,OAAA;UAAGyB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACb,YAAY,CAACc,OAAO,IAAIC,MAAM,CAACC,IAAI,CAAChB,YAAY,CAACc,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MAC3EhB,OAAO,CAACiB,IAAI,CAAC,gCAAgC,EAAElB,YAAY,CAAC;MAC5D,oBACEnB,OAAA;QAAKyB,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACzE1B,OAAA;UAAKyB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD9B,OAAA;UAAGyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5E9B,OAAA;UAAGyB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,eAAa,EAACP,YAAY,CAACmB,EAAE;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtE9B,OAAA;UAAGyB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,QAAM,EAACP,YAAY,CAACoB,IAAI;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAEV;IAEA,MAAMU,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACExC,OAAA;MAAKyB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,EAC9DQ,MAAM,CAACO,OAAO,CAACtB,YAAY,CAACc,OAAO,CAAC,CAACS,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QACjE,MAAMC,SAAS,GAAGjD,UAAU,CAAC8C,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;QACxC,MAAMC,WAAW,GAAGnD,UAAU,CAAC+C,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;QAC5C,MAAME,KAAK,GAAGT,YAAY,CAACK,KAAK,CAAC,IAAIC,SAAS;QAC9C,MAAMI,UAAU,GAAGnC,aAAa,KAAK+B,SAAS;;QAE9C;QACA,IAAI,CAACE,WAAW,EAAE,OAAO,IAAI;QAE7B,oBACEhD,OAAA,CAACT,MAAM,CAAC4D,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAACwB,SAAS,CAAE;UAC7CO,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAM,CAAE;UAC7BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAM,CAAE;UAC3B7B,SAAS,EAAG,6OACVyB,UAAU,GACN,gFAAgF,GAChF,kHACL,EAAE;UAAAxB,QAAA,eAEH1B,OAAA;YAAKyB,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjE1B,OAAA;cAAKyB,SAAS,EAAG,4NACfyB,UAAU,GACN,kCAAkC,GAClC,2BACL,EAAE;cAAAxB,QAAA,EACAuB;YAAK;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAMyB,SAAS,EAAG,0HAChByB,UAAU,GAAG,YAAY,GAAG,eAC7B,EAAE;cAAAxB,QAAA,EACAsB;YAAW;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACNoB,UAAU,iBACTlD,OAAA,CAACL,OAAO;cAAC8B,SAAS,EAAC;YAA4E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1BDgB,SAAS;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BD,CAAC;MAEpB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAM0B,eAAe,GAAGA,CAAA,kBACtBxD,OAAA;IAAKyB,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAClD1B,OAAA;MAAOyB,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACrF1B,OAAA;QAAKyB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C1B,OAAA;UAAMyB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D9B,OAAA;UAAA0B,QAAA,EAAM;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACR9B,OAAA;MAAKyB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB1B,OAAA;QACEuC,IAAI,EAAC,MAAM;QACXK,KAAK,EAAE7B,aAAc;QACrB0C,QAAQ,EAAGC,CAAC,IAAKpC,kBAAkB,CAACoC,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;QACpDgB,WAAW,EAAC,0BAA0B;QACtCnC,SAAS,EAAC;MAA+b;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1c,CAAC,eACF9B,OAAA;QAAKyB,SAAS,EAAC,2EAA2E;QAAAC,QAAA,EACvFX,aAAa,gBACZf,OAAA,CAACL,OAAO;UAAC8B,SAAS,EAAC;QAAoD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE1E9B,OAAA;UAAKyB,SAAS,EAAC;QAA8D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACpF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAM+B,cAAc,GAAGA,CAAA,kBACrB7D,OAAA;IAAKyB,SAAS,EAAC,kEAAkE;IAAAC,QAAA,gBAC/E1B,OAAA;MAAKyB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvD9B,OAAA;MAAGyB,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAAC;IAAiC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACzE9B,OAAA;MAAGyB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,GAAC,iBAAe,EAACP,YAAY,CAACoB,IAAI;IAAA;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7E9B,OAAA;MAAGyB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,GAAC,eAAa,EAACP,YAAY,CAACc,OAAO,GAAG,KAAK,GAAG,IAAI;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7F9B,OAAA;MAAGyB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,GAAC,iBAAe,EAACP,YAAY,CAACc,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAChB,YAAY,CAACc,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/H9B,OAAA;MAAKyB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjE1B,OAAA;QAAA0B,QAAA,EAAQ;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAAA9B,OAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxC9B,OAAA;QAAA0B,QAAA,GAAMoC,IAAI,CAACC,SAAS,CAAC5C,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC6C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE9B,OAAA;IAAKyB,SAAS,EAAC,qIAAqI;IAAAC,QAAA,gBAElJ1B,OAAA;MAAKyB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9D1B,OAAA,CAACT,MAAM,CAAC0E,GAAG;QACTxC,SAAS,EAAC,qDAAqD;QAC/DyC,OAAO,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACtBC,OAAO,EAAE;UAAED,KAAK,EAAG,GAAE3C,kBAAmB;QAAG,CAAE;QAC7C6C,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9B,OAAA;MAAKyB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF1B,OAAA;QAAKyB,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1E1B,OAAA;UAAKyB,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAE1D1B,OAAA;YAAKyB,SAAS,EAAG,2FACfZ,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;YAAAa,QAAA,gBACD1B,OAAA,CAACR,OAAO;cAACiC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B9B,OAAA;cAAA0B,QAAA,EAAO5B,UAAU,CAACW,QAAQ;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtC1B,OAAA;cAAIyB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAE7B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,GAClFrB,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAE1E1B,OAAA;YAAKyB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B1B,OAAA;cAAIyB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAE7B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7F9B,OAAA;cAAGyB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAG,6FACfZ,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;YAAAa,QAAA,gBACD1B,OAAA,CAACR,OAAO;cAACiC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B9B,OAAA;cAAA0B,QAAA,EAAO5B,UAAU,CAACW,QAAQ;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,GAClFrB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/D1B,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1B,OAAA;cAAIyB,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAE7B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzG9B,OAAA;cAAGyB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClC1B,OAAA;cAAKyB,SAAS,EAAG,sHACfZ,aAAa,GACT,+DAA+D,GAC/D,oDACL,EAAE;cAAAa,QAAA,gBACD1B,OAAA,CAACR,OAAO;gBAACiC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C9B,OAAA;gBAAA0B,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB9B,OAAA;gBAAA0B,QAAA,EAAO5B,UAAU,CAACW,QAAQ;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B1B,OAAA;cAAKyB,SAAS,EAAC,mGAAmG;cAAAC,QAAA,GAAC,WACxG,EAACrB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKyB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF1B,OAAA;QAAKyB,SAAS,EAAC,6EAA6E;QAAAC,QAAA,eAC1F1B,OAAA,CAACT,MAAM,CAAC0E,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B7C,SAAS,EAAC,0IAA0I;UAAAC,QAAA,gBAIpJ1B,OAAA;YAAKyB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C1B,OAAA;cAAKyB,SAAS,EAAC,iNAAiN;cAAAC,QAAA,eAC9N1B,OAAA;gBAAA0B,QAAA,GAAM,WAAS,EAACrB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9B,OAAA;YAAKyB,SAAS,EAAC,qJAAqJ;YAAAC,QAAA,EACjKP,YAAY,CAACY;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAGLX,YAAY,CAACsD,KAAK,iBACjBzE,OAAA;YAAKyB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnF1B,OAAA;cAAKyB,SAAS,EAAC,+HAA+H;cAAAC,QAAA,eAC5I1B,OAAA;gBACE0E,GAAG,EAAEvD,YAAY,CAACsD,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACdlD,SAAS,EAAC;cAAkI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD9B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAAAC,QAAA,GAE1BkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC9E,OAAA;cAAKyB,SAAS,EAAC,mEAAmE;cAAAC,QAAA,gBAChF1B,OAAA;gBAAA0B,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAA9B,OAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAC3B,EAACX,YAAY,CAACoB,IAAI,eAACvC,OAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,mBACf,EAACX,YAAY,CAACc,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAChB,YAAY,CAACc,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,eAACpC,OAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAC5E,EAACX,YAAY,CAACc,OAAO,GAAG,KAAK,GAAG,IAAI,eAACjC,OAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAC1C,EAACX,YAAY,CAACmB,EAAE;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAEAX,YAAY,CAACoB,IAAI,KAAK,KAAK,IAAIL,MAAM,CAACC,IAAI,CAAChB,YAAY,CAACc,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GAAGJ,SAAS,CAAC,CAAC,GAAGwB,eAAe,CAAC,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC;QAAA,GA9CDzB,aAAa;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+CR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKyB,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACxE1B,OAAA;QAAKyB,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElE1B,OAAA;UAAKyB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE1B,OAAA;YACEoD,OAAO,EAAEzC,UAAW;YACpBoE,QAAQ,EAAE1E,aAAa,KAAK,CAAE;YAC9BoB,SAAS,EAAG,oIACVpB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6EACL,EAAE;YAAAqB,QAAA,gBAEH1B,OAAA,CAACP,WAAW;cAACgC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC9B,OAAA;cAAA0B,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAGT9B,OAAA;YAAKyB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACT,UAAU,gBACVjB,OAAA;cAAKyB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtH1B,OAAA;gBAAA0B,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf9B,OAAA;gBAAA0B,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,gBAEN9B,OAAA;cAAKyB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtH1B,OAAA,CAACL,OAAO;gBAAC8B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B9B,OAAA;gBAAA0B,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9B,OAAA;YACEoD,OAAO,EAAE1C,MAAO;YAChBqE,QAAQ,EAAE,CAAC9D,UAAW;YACtBQ,SAAS,EAAG,oIACV,CAACR,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4EAA4E,GAC5E,0EACP,EAAE;YAAAoB,QAAA,EAEFrB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA,CAACL,OAAO;gBAAC8B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B9B,OAAA;gBAAA0B,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACnB,CAAC,gBAEH9B,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB9B,OAAA,CAACN,YAAY;gBAAC+B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1E1B,OAAA;YACEoD,OAAO,EAAEzC,UAAW;YACpBoE,QAAQ,EAAE1E,aAAa,KAAK,CAAE;YAC9BoB,SAAS,EAAG,qIACVpB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6EACL,EAAE;YAAAqB,QAAA,gBAEH1B,OAAA,CAACP,WAAW;cAACgC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC9B,OAAA;cAAA0B,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGT9B,OAAA;YAAKyB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACT,UAAU,gBACVjB,OAAA;cAAKyB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtH1B,OAAA;gBAAA0B,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf9B,OAAA;gBAAA0B,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,gBAEN9B,OAAA;cAAKyB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtH1B,OAAA,CAACL,OAAO;gBAAC8B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B9B,OAAA;gBAAA0B,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9B,OAAA;YACEoD,OAAO,EAAE1C,MAAO;YAChBqE,QAAQ,EAAE,CAAC9D,UAAW;YACtBQ,SAAS,EAAG,qIACV,CAACR,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4EAA4E,GAC5E,0EACP,EAAE;YAAAoB,QAAA,EAEFrB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA,CAACL,OAAO;gBAAC8B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B9B,OAAA;gBAAA0B,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACnB,CAAC,gBAEH9B,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB9B,OAAA,CAACN,YAAY;gBAAC+B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE1B,OAAA;YACEoD,OAAO,EAAEzC,UAAW;YACpBoE,QAAQ,EAAE1E,aAAa,KAAK,CAAE;YAC9BoB,SAAS,EAAG,kKACVpB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6EACL,EAAE;YAAAqB,QAAA,gBAEH1B,OAAA,CAACP,WAAW;cAACgC,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD9B,OAAA;cAAA0B,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGT9B,OAAA;YAAKyB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACT,UAAU,gBACVjB,OAAA;cAAKyB,SAAS,EAAC,sIAAsI;cAAAC,QAAA,gBACnJ1B,OAAA;gBAAA0B,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf9B,OAAA;gBAAA0B,QAAA,EAAM;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,gBAEN9B,OAAA;cAAKyB,SAAS,EAAC,sIAAsI;cAAAC,QAAA,gBACnJ1B,OAAA,CAACL,OAAO;gBAAC8B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B9B,OAAA;gBAAA0B,QAAA,EAAM;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9B,OAAA;YACEoD,OAAO,EAAE1C,MAAO;YAChBqE,QAAQ,EAAE,CAAC9D,UAAW;YACtBQ,SAAS,EAAG,kKACV,CAACR,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4EAA4E,GAC5E,0EACP,EAAE;YAAAoB,QAAA,EAEFrB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA,CAACL,OAAO;gBAAC8B,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C9B,OAAA;gBAAA0B,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB,CAAC,gBAEH9B,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B9B,OAAA,CAACN,YAAY;gBAAC+B,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eAClD;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA1eIX,YAAY;AAAA6E,EAAA,GAAZ7E,YAAY;AA4elB,eAAeA,YAAY;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}