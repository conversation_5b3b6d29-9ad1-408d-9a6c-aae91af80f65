{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n// Removed CSS import to avoid overflow conflicts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Safety check for question data\n  if (!question) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading question...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n\n  // Additional safety check for question data\n  if (!questionData || !questionData.name) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-amber-600 text-4xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-amber-800 mb-2\",\n          children: \"Question Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-600\",\n          children: \"This question could not be loaded properly.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"mt-4 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700\",\n          children: \"Refresh Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Debug logging\n  console.log('🎯 QuizRenderer Debug:', {\n    questionIndex,\n    totalQuestions,\n    question: question,\n    questionData: questionData,\n    selectedAnswer,\n    timeLeft\n  });\n\n  // Make question data available globally for debugging\n  window.lastQuestionData = questionData;\n  window.lastQuestion = question;\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Check if question data is valid\n  if (!question) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"No Question Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Question data is missing. Please check the quiz configuration.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Question Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"This question could not be loaded. Please try refreshing the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full h-1 bg-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-500\",\n        style: {\n          width: `${progressPercentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b border-gray-200 px-4 py-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-sm font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold text-gray-900\",\n            children: safeString(examTitle, 'Quiz')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\",\n          children: [questionIndex + 1, \" of \", totalQuestions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-6 mb-6 border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6 leading-relaxed\",\n            children: questionData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full h-auto max-h-64 rounded-lg shadow-md object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: questionData.options && Object.keys(questionData.options).length > 0 ? Object.entries(questionData.options).map(([key, value], index) => {\n              const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n              const label = optionLabels[index] || key;\n              const isSelected = currentAnswer === key;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleAnswerSelect(key),\n                className: `w-full text-left p-4 rounded-xl border-2 transition-all duration-300 cursor-pointer min-h-[64px] ${isSelected ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-600'}`,\n                    children: label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex-1 font-medium\",\n                    children: value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this), isSelected && /*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this);\n            }) :\n            /*#__PURE__*/\n            // Fill in the blank question\n            _jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: currentAnswer,\n                onChange: e => handleAnswerSelect(e.target.value),\n                placeholder: \"Type your answer here...\",\n                className: \"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all text-lg font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t border-gray-200 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 py-2 rounded-lg text-sm border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Please select an answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-lg text-sm border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Answer selected\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionData", "name", "onClick", "window", "location", "reload", "console", "log", "lastQuestionData", "lastQuestion", "handleAnswerSelect", "answer", "progressPercentage", "style", "width", "image", "src", "alt", "options", "Object", "keys", "length", "entries", "map", "key", "value", "index", "optionLabels", "label", "isSelected", "type", "onChange", "e", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\n// Removed CSS import to avoid overflow conflicts\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Safety check for question data\n  if (!question) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading question...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n\n  // Additional safety check for question data\n  if (!questionData || !questionData.name) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-amber-600 text-4xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-bold text-amber-800 mb-2\">Question Not Available</h3>\n          <p className=\"text-amber-600\">This question could not be loaded properly.</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700\"\n          >\n            Refresh Page\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Debug logging\n  console.log('🎯 QuizRenderer Debug:', {\n    questionIndex,\n    totalQuestions,\n    question: question,\n    questionData: questionData,\n    selectedAnswer,\n    timeLeft\n  });\n\n  // Make question data available globally for debugging\n  window.lastQuestionData = questionData;\n  window.lastQuestion = question;\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Check if question data is valid\n  if (!question) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">No Question Data</h3>\n          <p className=\"text-gray-600\">Question data is missing. Please check the quiz configuration.</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Question Not Available</h3>\n          <p className=\"text-gray-600\">This question could not be loaded. Please try refreshing the page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-auto\">\n      {/* Progress Bar */}\n      <div className=\"w-full h-1 bg-gray-200\">\n        <div\n          className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-500\"\n          style={{ width: `${progressPercentage}%` }}\n        />\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-white shadow-lg border-b border-gray-200 px-4 py-3\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          {/* Timer */}\n          <div className={`flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-sm font-bold transition-all ${\n            isTimeWarning\n              ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n              : 'bg-blue-100 text-blue-700 border border-blue-300'\n          }`}>\n            <TbClock className=\"w-4 h-4\" />\n            <span>{formatTime(timeLeft)}</span>\n          </div>\n\n          {/* Quiz Title */}\n          <div className=\"text-center\">\n            <h1 className=\"text-lg font-bold text-gray-900\">{safeString(examTitle, 'Quiz')}</h1>\n          </div>\n\n          {/* Question Counter */}\n          <div className=\"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\">\n            {questionIndex + 1} of {totalQuestions}\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-6 mb-6 border border-gray-100\">\n\n            {/* Question Number Badge */}\n            <div className=\"mb-6\">\n              <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg\">\n                <span>Question {questionIndex + 1} of {totalQuestions}</span>\n              </div>\n            </div>\n\n            {/* Question Text */}\n            <div className=\"text-xl font-semibold text-gray-900 mb-6 leading-relaxed\">\n              {questionData.name}\n            </div>\n\n            {/* Question Image */}\n            {questionData.image && (\n              <div className=\"mb-6 text-center\">\n                <div className=\"inline-block bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm\">\n                  <img\n                    src={questionData.image}\n                    alt=\"Question\"\n                    className=\"max-w-full h-auto max-h-64 rounded-lg shadow-md object-contain\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Answer Options */}\n            <div className=\"space-y-3\">\n              {questionData.options && Object.keys(questionData.options).length > 0 ? (\n                Object.entries(questionData.options).map(([key, value], index) => {\n                  const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                  const label = optionLabels[index] || key;\n                  const isSelected = currentAnswer === key;\n\n                  return (\n                    <div\n                      key={key}\n                      onClick={() => handleAnswerSelect(key)}\n                      className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 cursor-pointer min-h-[64px] ${\n                        isSelected\n                          ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300'\n                          : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'\n                      }`}\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n                          isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-600'\n                        }`}>\n                          {label}\n                        </div>\n                        <span className=\"flex-1 font-medium\">{value}</span>\n                        {isSelected && <TbCheck className=\"w-5 h-5\" />}\n                      </div>\n                    </div>\n                  );\n                })\n              ) : (\n                // Fill in the blank question\n                <div className=\"space-y-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-lg\">✏️</span>\n                      <span>Your Answer:</span>\n                    </div>\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={currentAnswer}\n                    onChange={(e) => handleAnswerSelect(e.target.value)}\n                    placeholder=\"Type your answer here...\"\n                    className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all text-lg font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg\"\n                  />\n                </div>\n              )}\n            </div>\n\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"bg-white border-t border-gray-200 shadow-lg\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between gap-4\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              <span>Previous</span>\n            </button>\n\n            {/* Answer Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 py-2 rounded-lg text-sm border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Please select an answer</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-lg text-sm border border-green-200\">\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Answer selected</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-5 h-5\" />\n                  <span>Submit Quiz</span>\n                </>\n              ) : (\n                <>\n                  <span>Next</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AACpF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAACkB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,IAAI,CAACe,QAAQ,EAAE;IACb,oBACEJ,OAAA;MAAKmB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGpB,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpB,OAAA;UAAKmB,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FxB,OAAA;UAAGmB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,YAAY,GAAG7B,mBAAmB,CAACQ,QAAQ,CAAC;;EAElD;EACA,IAAI,CAACqB,YAAY,IAAI,CAACA,YAAY,CAACC,IAAI,EAAE;IACvC,oBACE1B,OAAA;MAAKmB,SAAS,EAAC,6FAA6F;MAAAC,QAAA,eAC1GpB,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpB,OAAA;UAAKmB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtDxB,OAAA;UAAImB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFxB,OAAA;UAAGmB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7ExB,OAAA;UACE2B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCX,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACAO,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;IACpC3B,aAAa;IACbC,cAAc;IACdF,QAAQ,EAAEA,QAAQ;IAClBqB,YAAY,EAAEA,YAAY;IAC1BlB,cAAc;IACdE;EACF,CAAC,CAAC;;EAEF;EACAmB,MAAM,CAACK,gBAAgB,GAAGR,YAAY;EACtCG,MAAM,CAACM,YAAY,GAAG9B,QAAQ;EAE9Bd,SAAS,CAAC,MAAM;IACd0B,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAM8B,kBAAkB,GAAIC,MAAM,IAAK;IACrCpB,gBAAgB,CAACoB,MAAM,CAAC;IACxBlB,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAAC4B,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAAChC,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEJ,OAAA;MAAKmB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGpB,OAAA;QAAKmB,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC9EpB,OAAA;UAAKmB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDxB,OAAA;UAAImB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ExB,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACC,YAAY,CAACC,IAAI,IAAID,YAAY,CAACC,IAAI,KAAK,wBAAwB,EAAE;IACxE,oBACE1B,OAAA;MAAKmB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGpB,OAAA;QAAKmB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DpB,OAAA;UAAKmB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDxB,OAAA;UAAImB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFxB,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExB,OAAA;IAAKmB,SAAS,EAAC,mFAAmF;IAAAC,QAAA,gBAEhGpB,OAAA;MAAKmB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCpB,OAAA;QACEmB,SAAS,EAAC,iFAAiF;QAC3FmB,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAEF,kBAAmB;QAAG;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxB,OAAA;MAAKmB,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpEpB,OAAA;QAAKmB,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElEpB,OAAA;UAAKmB,SAAS,EAAG,2FACfN,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;UAAAO,QAAA,gBACDpB,OAAA,CAACR,OAAO;YAAC2B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BxB,OAAA;YAAAoB,QAAA,EAAOtB,UAAU,CAACW,QAAQ;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAGNxB,OAAA;UAAKmB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpB,OAAA;YAAImB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEvB,UAAU,CAACe,SAAS,EAAE,MAAM;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAGNxB,OAAA;UAAKmB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,GAClFf,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKmB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFpB,OAAA;QAAKmB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CpB,OAAA;UAAKmB,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAG7EpB,OAAA;YAAKmB,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBpB,OAAA;cAAKmB,SAAS,EAAC,+IAA+I;cAAAC,QAAA,eAC5JpB,OAAA;gBAAAoB,QAAA,GAAM,WAAS,EAACf,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxB,OAAA;YAAKmB,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACtEK,YAAY,CAACC;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAGLC,YAAY,CAACe,KAAK,iBACjBxC,OAAA;YAAKmB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BpB,OAAA;cAAKmB,SAAS,EAAC,yEAAyE;cAAAC,QAAA,eACtFpB,OAAA;gBACEyC,GAAG,EAAEhB,YAAY,CAACe,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACdvB,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDxB,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBK,YAAY,CAACkB,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACkB,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GACnEF,MAAM,CAACG,OAAO,CAACtB,YAAY,CAACkB,OAAO,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;cAChE,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACnD,MAAMC,KAAK,GAAGD,YAAY,CAACD,KAAK,CAAC,IAAIF,GAAG;cACxC,MAAMK,UAAU,GAAGvC,aAAa,KAAKkC,GAAG;cAExC,oBACEjD,OAAA;gBAEE2B,OAAO,EAAEA,CAAA,KAAMQ,kBAAkB,CAACc,GAAG,CAAE;gBACvC9B,SAAS,EAAG,oGACVmC,UAAU,GACN,uEAAuE,GACvE,+FACL,EAAE;gBAAAlC,QAAA,eAEHpB,OAAA;kBAAKmB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCpB,OAAA;oBAAKmB,SAAS,EAAG,2EACfmC,UAAU,GAAG,wBAAwB,GAAG,2BACzC,EAAE;oBAAAlC,QAAA,EACAiC;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNxB,OAAA;oBAAMmB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE8B;kBAAK;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAClD8B,UAAU,iBAAItD,OAAA,CAACL,OAAO;oBAACwB,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC,GAhBDyB,GAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBL,CAAC;YAEV,CAAC,CAAC;YAAA;YAEF;YACAxB,OAAA;cAAKmB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpB,OAAA;gBAAOmB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,eAC7DpB,OAAA;kBAAKmB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCpB,OAAA;oBAAMmB,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCxB,OAAA;oBAAAoB,QAAA,EAAM;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRxB,OAAA;gBACEuD,IAAI,EAAC,MAAM;gBACXL,KAAK,EAAEnC,aAAc;gBACrByC,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAACsB,CAAC,CAACC,MAAM,CAACR,KAAK,CAAE;gBACpDS,WAAW,EAAC,0BAA0B;gBACtCxC,SAAS,EAAC;cAAmM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9M,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKmB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DpB,OAAA;QAAKmB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CpB,OAAA;UAAKmB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACtDpB,OAAA;YACE2B,OAAO,EAAEhB,UAAW;YACpBiD,QAAQ,EAAEvD,aAAa,KAAK,CAAE;YAC9Bc,SAAS,EAAG,6EACVd,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAAe,QAAA,gBAEHpB,OAAA,CAACP,WAAW;cAAC0B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCxB,OAAA;cAAAoB,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGTxB,OAAA;YAAKmB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACH,UAAU,gBACVjB,OAAA;cAAKmB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHpB,OAAA;gBAAAoB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfxB,OAAA;gBAAAoB,QAAA,EAAM;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,gBAENxB,OAAA;cAAKmB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHpB,OAAA,CAACL,OAAO;gBAACwB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BxB,OAAA;gBAAAoB,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxB,OAAA;YACE2B,OAAO,EAAEjB,MAAO;YAChBkD,QAAQ,EAAE,CAAC3C,UAAW;YACtBE,SAAS,EAAG,6EACV,CAACF,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4DAA4D,GAC5D,0DACP,EAAE;YAAAc,QAAA,EAEFf,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAkB,QAAA,gBACEpB,OAAA,CAACL,OAAO;gBAACwB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BxB,OAAA;gBAAAoB,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB,CAAC,gBAEHxB,OAAA,CAAAE,SAAA;cAAAkB,QAAA,gBACEpB,OAAA;gBAAAoB,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjBxB,OAAA,CAACN,YAAY;gBAACyB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CAzRIX,YAAY;AAAA0D,EAAA,GAAZ1D,YAAY;AA2RlB,eAAeA,YAAY;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}