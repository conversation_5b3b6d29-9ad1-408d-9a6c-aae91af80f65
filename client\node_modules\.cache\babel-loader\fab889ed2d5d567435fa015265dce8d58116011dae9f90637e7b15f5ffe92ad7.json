{"ast": null, "code": "/**\n * Safe data extraction utilities for Quiz UI components\n * Prevents \"Objects are not valid as a React child\" errors\n */\n\n/**\n * Safely extracts a string value from an object property\n * @param {any} value - The value to extract\n * @param {string} fallback - Fallback value if extraction fails\n * @returns {string} - Safe string value\n */\nexport const safeString = (value, fallback = '') => {\n  if (typeof value === 'string') return value;\n  if (typeof value === 'number') return String(value);\n  if (value === null || value === undefined) return fallback;\n  if (typeof value === 'boolean') return String(value);\n  return fallback;\n};\n\n/**\n * Safely extracts a number value from an object property\n * @param {any} value - The value to extract\n * @param {number} fallback - Fallback value if extraction fails\n * @returns {number} - Safe number value\n */\nexport const safeNumber = (value, fallback = 0) => {\n  if (typeof value === 'number' && !isNaN(value)) return value;\n  if (typeof value === 'string') {\n    const parsed = parseFloat(value);\n    return !isNaN(parsed) ? parsed : fallback;\n  }\n  return fallback;\n};\n\n/**\n * Safely extracts quiz properties for rendering\n * @param {Object} quiz - Quiz object\n * @returns {Object} - Safe quiz properties\n */\nexport const extractQuizData = quiz => {\n  if (!quiz || typeof quiz !== 'object') {\n    return {\n      id: '',\n      name: 'Unknown Quiz',\n      duration: 30,\n      totalQuestions: 0,\n      totalXP: 100,\n      subject: 'General',\n      class: 'N/A',\n      difficulty: 'medium',\n      category: 'General',\n      description: 'Test your knowledge with this quiz',\n      passingMarks: 60,\n      topic: '',\n      questions: []\n    };\n  }\n  return {\n    id: safeString(quiz._id || quiz.id),\n    name: safeString(quiz.name, 'Unknown Quiz'),\n    duration: safeNumber(quiz.duration, 30),\n    totalQuestions: Array.isArray(quiz.questions) ? quiz.questions.length : safeNumber(quiz.totalQuestions, 0),\n    totalXP: safeNumber(quiz.xpPoints || quiz.totalXP || quiz.totalMarks, 100),\n    subject: safeString(quiz.subject || quiz.category, 'General'),\n    class: safeString(quiz.class, 'N/A'),\n    difficulty: safeString(quiz.difficulty || quiz.difficultyLevel, 'medium'),\n    category: safeString(quiz.category, 'General'),\n    description: safeString(quiz.description, 'Test your knowledge with this quiz'),\n    passingMarks: safeNumber(quiz.passingMarks || quiz.passingPercentage, 60),\n    topic: safeString(quiz.topic),\n    questions: Array.isArray(quiz.questions) ? quiz.questions : []\n  };\n};\n\n/**\n * Safely extracts question properties for rendering\n * @param {Object} question - Question object\n * @returns {Object} - Safe question properties\n */\nexport const extractQuestionData = question => {\n  if (!question || typeof question !== 'object') {\n    return {\n      id: '',\n      name: 'Question not available',\n      type: 'mcq',\n      options: {},\n      correctAnswer: '',\n      image: '',\n      duration: 90,\n      difficulty: 'medium'\n    };\n  }\n\n  // Handle different question data structures\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n\n  // Convert answerType to our standard types\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n  const extractedData = {\n    id: safeString(question._id || question.id),\n    name: safeString(questionText, 'Question not available'),\n    type: normalizedType,\n    options: questionOptions && typeof questionOptions === 'object' ? questionOptions : {},\n    correctAnswer: safeString(question.correctAnswer || question.correctOption || question.answer),\n    image: safeString(question.image || question.imageUrl || question.img),\n    duration: safeNumber(question.duration, 90),\n    difficulty: safeString(question.difficultyLevel || question.difficulty, 'medium')\n  };\n  return extractedData;\n};\n\n/**\n * Safely extracts user result properties for rendering\n * @param {Object} userResult - User result object\n * @returns {Object} - Safe user result properties\n */\nexport const extractUserResultData = userResult => {\n  if (!userResult || typeof userResult !== 'object') {\n    return {\n      score: 0,\n      percentage: 0,\n      verdict: 'Not Attempted',\n      correctAnswers: 0,\n      wrongAnswers: 0,\n      totalQuestions: 0,\n      timeSpent: 0,\n      xpGained: 0,\n      passed: false\n    };\n  }\n  const percentage = safeNumber(userResult.percentage || userResult.score, 0);\n  const passingMarks = safeNumber(userResult.passingMarks, 60);\n  return {\n    score: safeNumber(userResult.score || userResult.percentage, 0),\n    percentage: percentage,\n    verdict: safeString(userResult.verdict, percentage >= passingMarks ? 'Pass' : 'Fail'),\n    correctAnswers: Array.isArray(userResult.correctAnswers) ? userResult.correctAnswers.length : safeNumber(userResult.correctAnswers, 0),\n    wrongAnswers: Array.isArray(userResult.wrongAnswers) ? userResult.wrongAnswers.length : safeNumber(userResult.wrongAnswers, 0),\n    totalQuestions: safeNumber(userResult.totalQuestions, 0),\n    timeSpent: safeNumber(userResult.timeSpent, 0),\n    xpGained: safeNumber(userResult.points || userResult.xpGained, 0),\n    passed: percentage >= passingMarks\n  };\n};\n\n/**\n * Gets quiz status for UI display\n * @param {Object} userResult - User result object\n * @param {number} passingMarks - Passing marks threshold\n * @returns {Object} - Status configuration\n */\nexport const getQuizStatus = (userResult, passingMarks = 60) => {\n  if (!userResult) {\n    return {\n      status: 'not_attempted',\n      statusColor: 'bg-blue-500',\n      cardBg: 'bg-blue-50 hover:bg-blue-100',\n      borderColor: 'border-blue-200 hover:border-blue-300',\n      textColor: 'text-blue-900'\n    };\n  }\n  const resultData = extractUserResultData(userResult);\n  const passed = resultData.percentage >= passingMarks;\n  if (passed) {\n    return {\n      status: 'passed',\n      statusColor: 'bg-green-500',\n      cardBg: 'bg-green-50 hover:bg-green-100',\n      borderColor: 'border-green-200 hover:border-green-300',\n      textColor: 'text-green-900'\n    };\n  } else {\n    return {\n      status: 'failed',\n      statusColor: 'bg-red-500',\n      cardBg: 'bg-red-50 hover:bg-red-100',\n      borderColor: 'border-red-200 hover:border-red-300',\n      textColor: 'text-red-900'\n    };\n  }\n};\n\n/**\n * Safely renders a value for JSX (prevents object rendering errors)\n * @param {any} value - Value to render\n * @param {string} fallback - Fallback if value is not renderable\n * @returns {string|number} - Safe renderable value\n */\nexport const safeRender = (value, fallback = '') => {\n  if (typeof value === 'string' || typeof value === 'number') {\n    return value;\n  }\n  return fallback;\n};\n\n/**\n * Formats time in minutes and seconds\n * @param {number} seconds - Time in seconds\n * @returns {string} - Formatted time string\n */\nexport const formatTime = seconds => {\n  const minutes = Math.floor(safeNumber(seconds, 0) / 60);\n  const remainingSeconds = safeNumber(seconds, 0) % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\n/**\n * Validates if a value is safe to render in JSX\n * @param {any} value - Value to check\n * @returns {boolean} - True if safe to render\n */\nexport const isSafeToRender = value => {\n  return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean';\n};\n\n/**\n * Safe conditional renderer - only renders if value is safe\n * @param {any} value - Value to render\n * @param {string} fallback - Fallback if value is not safe\n * @returns {string|number|null} - Safe value or null\n */\nexport const conditionalRender = (value, fallback = '') => {\n  if (isSafeToRender(value)) {\n    return value;\n  }\n  if (isSafeToRender(fallback)) {\n    return fallback;\n  }\n  return null;\n};\n\n/**\n * Safely extracts array length\n * @param {any} arr - Array to check\n * @returns {number} - Safe array length\n */\nexport const safeArrayLength = arr => {\n  return Array.isArray(arr) ? arr.length : 0;\n};\n\n/**\n * Safely extracts object keys count\n * @param {any} obj - Object to check\n * @returns {number} - Safe object keys count\n */\nexport const safeObjectKeysCount = obj => {\n  return obj && typeof obj === 'object' && !Array.isArray(obj) ? Object.keys(obj).length : 0;\n};", "map": {"version": 3, "names": ["safeString", "value", "fallback", "String", "undefined", "safeNumber", "isNaN", "parsed", "parseFloat", "extractQuizData", "quiz", "id", "name", "duration", "totalQuestions", "totalXP", "subject", "class", "difficulty", "category", "description", "passingMarks", "topic", "questions", "_id", "Array", "isArray", "length", "xpPoints", "totalMarks", "difficultyLevel", "passingPercentage", "extractQuestionData", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "image", "questionText", "text", "title", "questionType", "answerType", "questionOptions", "choices", "answers", "normalizedType", "typeString", "toLowerCase", "includes", "Object", "keys", "extractedData", "correctOption", "answer", "imageUrl", "img", "extractUserResultData", "userResult", "score", "percentage", "verdict", "correctAnswers", "wrongAnswers", "timeSpent", "xpGained", "passed", "points", "getQuizStatus", "status", "statusColor", "cardBg", "borderColor", "textColor", "resultData", "safeRender", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "isSafeToRender", "conditionalRender", "safeArray<PERSON><PERSON><PERSON>", "arr", "safeObjectKeysCount", "obj"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/utils/quizDataUtils.js"], "sourcesContent": ["/**\n * Safe data extraction utilities for Quiz UI components\n * Prevents \"Objects are not valid as a React child\" errors\n */\n\n/**\n * Safely extracts a string value from an object property\n * @param {any} value - The value to extract\n * @param {string} fallback - Fallback value if extraction fails\n * @returns {string} - Safe string value\n */\nexport const safeString = (value, fallback = '') => {\n  if (typeof value === 'string') return value;\n  if (typeof value === 'number') return String(value);\n  if (value === null || value === undefined) return fallback;\n  if (typeof value === 'boolean') return String(value);\n  return fallback;\n};\n\n/**\n * Safely extracts a number value from an object property\n * @param {any} value - The value to extract\n * @param {number} fallback - Fallback value if extraction fails\n * @returns {number} - Safe number value\n */\nexport const safeNumber = (value, fallback = 0) => {\n  if (typeof value === 'number' && !isNaN(value)) return value;\n  if (typeof value === 'string') {\n    const parsed = parseFloat(value);\n    return !isNaN(parsed) ? parsed : fallback;\n  }\n  return fallback;\n};\n\n/**\n * Safely extracts quiz properties for rendering\n * @param {Object} quiz - Quiz object\n * @returns {Object} - Safe quiz properties\n */\nexport const extractQuizData = (quiz) => {\n  if (!quiz || typeof quiz !== 'object') {\n    return {\n      id: '',\n      name: 'Unknown Quiz',\n      duration: 30,\n      totalQuestions: 0,\n      totalXP: 100,\n      subject: 'General',\n      class: 'N/A',\n      difficulty: 'medium',\n      category: 'General',\n      description: 'Test your knowledge with this quiz',\n      passingMarks: 60,\n      topic: '',\n      questions: []\n    };\n  }\n\n  return {\n    id: safeString(quiz._id || quiz.id),\n    name: safeString(quiz.name, 'Unknown Quiz'),\n    duration: safeNumber(quiz.duration, 30),\n    totalQuestions: Array.isArray(quiz.questions) ? quiz.questions.length : safeNumber(quiz.totalQuestions, 0),\n    totalXP: safeNumber(quiz.xpPoints || quiz.totalXP || quiz.totalMarks, 100),\n    subject: safeString(quiz.subject || quiz.category, 'General'),\n    class: safeString(quiz.class, 'N/A'),\n    difficulty: safeString(quiz.difficulty || quiz.difficultyLevel, 'medium'),\n    category: safeString(quiz.category, 'General'),\n    description: safeString(quiz.description, 'Test your knowledge with this quiz'),\n    passingMarks: safeNumber(quiz.passingMarks || quiz.passingPercentage, 60),\n    topic: safeString(quiz.topic),\n    questions: Array.isArray(quiz.questions) ? quiz.questions : []\n  };\n};\n\n/**\n * Safely extracts question properties for rendering\n * @param {Object} question - Question object\n * @returns {Object} - Safe question properties\n */\nexport const extractQuestionData = (question) => {\n  if (!question || typeof question !== 'object') {\n    return {\n      id: '',\n      name: 'Question not available',\n      type: 'mcq',\n      options: {},\n      correctAnswer: '',\n      image: '',\n      duration: 90,\n      difficulty: 'medium'\n    };\n  }\n\n  // Handle different question data structures\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n\n  // Convert answerType to our standard types\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n\n  const extractedData = {\n    id: safeString(question._id || question.id),\n    name: safeString(questionText, 'Question not available'),\n    type: normalizedType,\n    options: questionOptions && typeof questionOptions === 'object' ? questionOptions : {},\n    correctAnswer: safeString(question.correctAnswer || question.correctOption || question.answer),\n    image: safeString(question.image || question.imageUrl || question.img),\n    duration: safeNumber(question.duration, 90),\n    difficulty: safeString(question.difficultyLevel || question.difficulty, 'medium')\n  };\n\n  return extractedData;\n};\n\n/**\n * Safely extracts user result properties for rendering\n * @param {Object} userResult - User result object\n * @returns {Object} - Safe user result properties\n */\nexport const extractUserResultData = (userResult) => {\n  if (!userResult || typeof userResult !== 'object') {\n    return {\n      score: 0,\n      percentage: 0,\n      verdict: 'Not Attempted',\n      correctAnswers: 0,\n      wrongAnswers: 0,\n      totalQuestions: 0,\n      timeSpent: 0,\n      xpGained: 0,\n      passed: false\n    };\n  }\n\n  const percentage = safeNumber(userResult.percentage || userResult.score, 0);\n  const passingMarks = safeNumber(userResult.passingMarks, 60);\n  \n  return {\n    score: safeNumber(userResult.score || userResult.percentage, 0),\n    percentage: percentage,\n    verdict: safeString(userResult.verdict, percentage >= passingMarks ? 'Pass' : 'Fail'),\n    correctAnswers: Array.isArray(userResult.correctAnswers) ? userResult.correctAnswers.length : safeNumber(userResult.correctAnswers, 0),\n    wrongAnswers: Array.isArray(userResult.wrongAnswers) ? userResult.wrongAnswers.length : safeNumber(userResult.wrongAnswers, 0),\n    totalQuestions: safeNumber(userResult.totalQuestions, 0),\n    timeSpent: safeNumber(userResult.timeSpent, 0),\n    xpGained: safeNumber(userResult.points || userResult.xpGained, 0),\n    passed: percentage >= passingMarks\n  };\n};\n\n/**\n * Gets quiz status for UI display\n * @param {Object} userResult - User result object\n * @param {number} passingMarks - Passing marks threshold\n * @returns {Object} - Status configuration\n */\nexport const getQuizStatus = (userResult, passingMarks = 60) => {\n  if (!userResult) {\n    return {\n      status: 'not_attempted',\n      statusColor: 'bg-blue-500',\n      cardBg: 'bg-blue-50 hover:bg-blue-100',\n      borderColor: 'border-blue-200 hover:border-blue-300',\n      textColor: 'text-blue-900'\n    };\n  }\n\n  const resultData = extractUserResultData(userResult);\n  const passed = resultData.percentage >= passingMarks;\n\n  if (passed) {\n    return {\n      status: 'passed',\n      statusColor: 'bg-green-500',\n      cardBg: 'bg-green-50 hover:bg-green-100',\n      borderColor: 'border-green-200 hover:border-green-300',\n      textColor: 'text-green-900'\n    };\n  } else {\n    return {\n      status: 'failed',\n      statusColor: 'bg-red-500',\n      cardBg: 'bg-red-50 hover:bg-red-100',\n      borderColor: 'border-red-200 hover:border-red-300',\n      textColor: 'text-red-900'\n    };\n  }\n};\n\n/**\n * Safely renders a value for JSX (prevents object rendering errors)\n * @param {any} value - Value to render\n * @param {string} fallback - Fallback if value is not renderable\n * @returns {string|number} - Safe renderable value\n */\nexport const safeRender = (value, fallback = '') => {\n  if (typeof value === 'string' || typeof value === 'number') {\n    return value;\n  }\n  return fallback;\n};\n\n/**\n * Formats time in minutes and seconds\n * @param {number} seconds - Time in seconds\n * @returns {string} - Formatted time string\n */\nexport const formatTime = (seconds) => {\n  const minutes = Math.floor(safeNumber(seconds, 0) / 60);\n  const remainingSeconds = safeNumber(seconds, 0) % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\n/**\n * Validates if a value is safe to render in JSX\n * @param {any} value - Value to check\n * @returns {boolean} - True if safe to render\n */\nexport const isSafeToRender = (value) => {\n  return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean';\n};\n\n/**\n * Safe conditional renderer - only renders if value is safe\n * @param {any} value - Value to render\n * @param {string} fallback - Fallback if value is not safe\n * @returns {string|number|null} - Safe value or null\n */\nexport const conditionalRender = (value, fallback = '') => {\n  if (isSafeToRender(value)) {\n    return value;\n  }\n  if (isSafeToRender(fallback)) {\n    return fallback;\n  }\n  return null;\n};\n\n/**\n * Safely extracts array length\n * @param {any} arr - Array to check\n * @returns {number} - Safe array length\n */\nexport const safeArrayLength = (arr) => {\n  return Array.isArray(arr) ? arr.length : 0;\n};\n\n/**\n * Safely extracts object keys count\n * @param {any} obj - Object to check\n * @returns {number} - Safe object keys count\n */\nexport const safeObjectKeysCount = (obj) => {\n  return obj && typeof obj === 'object' && !Array.isArray(obj) ? Object.keys(obj).length : 0;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,GAAGA,CAACC,KAAK,EAAEC,QAAQ,GAAG,EAAE,KAAK;EAClD,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOE,MAAM,CAACF,KAAK,CAAC;EACnD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKG,SAAS,EAAE,OAAOF,QAAQ;EAC1D,IAAI,OAAOD,KAAK,KAAK,SAAS,EAAE,OAAOE,MAAM,CAACF,KAAK,CAAC;EACpD,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,UAAU,GAAGA,CAACJ,KAAK,EAAEC,QAAQ,GAAG,CAAC,KAAK;EACjD,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,CAACK,KAAK,CAACL,KAAK,CAAC,EAAE,OAAOA,KAAK;EAC5D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMM,MAAM,GAAGC,UAAU,CAACP,KAAK,CAAC;IAChC,OAAO,CAACK,KAAK,CAACC,MAAM,CAAC,GAAGA,MAAM,GAAGL,QAAQ;EAC3C;EACA,OAAOA,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,eAAe,GAAIC,IAAI,IAAK;EACvC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,CAAC;MACjBC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,KAAK;MACZC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,SAAS;MACnBC,WAAW,EAAE,oCAAoC;MACjDC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,OAAO;IACLZ,EAAE,EAAEX,UAAU,CAACU,IAAI,CAACc,GAAG,IAAId,IAAI,CAACC,EAAE,CAAC;IACnCC,IAAI,EAAEZ,UAAU,CAACU,IAAI,CAACE,IAAI,EAAE,cAAc,CAAC;IAC3CC,QAAQ,EAAER,UAAU,CAACK,IAAI,CAACG,QAAQ,EAAE,EAAE,CAAC;IACvCC,cAAc,EAAEW,KAAK,CAACC,OAAO,CAAChB,IAAI,CAACa,SAAS,CAAC,GAAGb,IAAI,CAACa,SAAS,CAACI,MAAM,GAAGtB,UAAU,CAACK,IAAI,CAACI,cAAc,EAAE,CAAC,CAAC;IAC1GC,OAAO,EAAEV,UAAU,CAACK,IAAI,CAACkB,QAAQ,IAAIlB,IAAI,CAACK,OAAO,IAAIL,IAAI,CAACmB,UAAU,EAAE,GAAG,CAAC;IAC1Eb,OAAO,EAAEhB,UAAU,CAACU,IAAI,CAACM,OAAO,IAAIN,IAAI,CAACS,QAAQ,EAAE,SAAS,CAAC;IAC7DF,KAAK,EAAEjB,UAAU,CAACU,IAAI,CAACO,KAAK,EAAE,KAAK,CAAC;IACpCC,UAAU,EAAElB,UAAU,CAACU,IAAI,CAACQ,UAAU,IAAIR,IAAI,CAACoB,eAAe,EAAE,QAAQ,CAAC;IACzEX,QAAQ,EAAEnB,UAAU,CAACU,IAAI,CAACS,QAAQ,EAAE,SAAS,CAAC;IAC9CC,WAAW,EAAEpB,UAAU,CAACU,IAAI,CAACU,WAAW,EAAE,oCAAoC,CAAC;IAC/EC,YAAY,EAAEhB,UAAU,CAACK,IAAI,CAACW,YAAY,IAAIX,IAAI,CAACqB,iBAAiB,EAAE,EAAE,CAAC;IACzET,KAAK,EAAEtB,UAAU,CAACU,IAAI,CAACY,KAAK,CAAC;IAC7BC,SAAS,EAAEE,KAAK,CAACC,OAAO,CAAChB,IAAI,CAACa,SAAS,CAAC,GAAGb,IAAI,CAACa,SAAS,GAAG;EAC9D,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,mBAAmB,GAAIC,QAAQ,IAAK;EAC/C,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,OAAO;MACLtB,EAAE,EAAE,EAAE;MACNC,IAAI,EAAE,wBAAwB;MAC9BsB,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,CAAC,CAAC;MACXC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE,EAAE;MACTxB,QAAQ,EAAE,EAAE;MACZK,UAAU,EAAE;IACd,CAAC;EACH;;EAEA;EACA,MAAMoB,YAAY,GAAGL,QAAQ,CAACrB,IAAI,IAAIqB,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,KAAK,IAAI,EAAE;EAChG,MAAMC,YAAY,GAAGR,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACS,UAAU,IAAIT,QAAQ,CAACQ,YAAY,IAAI,KAAK;EAC3F,MAAME,eAAe,GAAGV,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACW,OAAO,IAAIX,QAAQ,CAACY,OAAO,IAAI,CAAC,CAAC;;EAEtF;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,UAAU,GAAG5C,MAAM,CAACsC,YAAY,CAAC,CAACO,WAAW,CAAC,CAAC;EAErD,IAAID,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,KAAK,KAAK,IAAIA,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAC7HH,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,KAAK,MAAM,IAAIA,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9HH,cAAc,GAAG,MAAM;EACzB,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;IACzEH,cAAc,GAAG,OAAO;EAC1B;;EAEA;EACA,IAAIA,cAAc,KAAK,KAAK,KAAK,CAACH,eAAe,IAAIO,MAAM,CAACC,IAAI,CAACR,eAAe,CAAC,CAAChB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC/FmB,cAAc,GAAG,MAAM;EACzB;EAEA,MAAMM,aAAa,GAAG;IACpBzC,EAAE,EAAEX,UAAU,CAACiC,QAAQ,CAACT,GAAG,IAAIS,QAAQ,CAACtB,EAAE,CAAC;IAC3CC,IAAI,EAAEZ,UAAU,CAACsC,YAAY,EAAE,wBAAwB,CAAC;IACxDJ,IAAI,EAAEY,cAAc;IACpBX,OAAO,EAAEQ,eAAe,IAAI,OAAOA,eAAe,KAAK,QAAQ,GAAGA,eAAe,GAAG,CAAC,CAAC;IACtFP,aAAa,EAAEpC,UAAU,CAACiC,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACoB,aAAa,IAAIpB,QAAQ,CAACqB,MAAM,CAAC;IAC9FjB,KAAK,EAAErC,UAAU,CAACiC,QAAQ,CAACI,KAAK,IAAIJ,QAAQ,CAACsB,QAAQ,IAAItB,QAAQ,CAACuB,GAAG,CAAC;IACtE3C,QAAQ,EAAER,UAAU,CAAC4B,QAAQ,CAACpB,QAAQ,EAAE,EAAE,CAAC;IAC3CK,UAAU,EAAElB,UAAU,CAACiC,QAAQ,CAACH,eAAe,IAAIG,QAAQ,CAACf,UAAU,EAAE,QAAQ;EAClF,CAAC;EAED,OAAOkC,aAAa;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAIC,UAAU,IAAK;EACnD,IAAI,CAACA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACjD,OAAO;MACLC,KAAK,EAAE,CAAC;MACRC,UAAU,EAAE,CAAC;MACbC,OAAO,EAAE,eAAe;MACxBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC;MACfjD,cAAc,EAAE,CAAC;MACjBkD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC;EACH;EAEA,MAAMN,UAAU,GAAGvD,UAAU,CAACqD,UAAU,CAACE,UAAU,IAAIF,UAAU,CAACC,KAAK,EAAE,CAAC,CAAC;EAC3E,MAAMtC,YAAY,GAAGhB,UAAU,CAACqD,UAAU,CAACrC,YAAY,EAAE,EAAE,CAAC;EAE5D,OAAO;IACLsC,KAAK,EAAEtD,UAAU,CAACqD,UAAU,CAACC,KAAK,IAAID,UAAU,CAACE,UAAU,EAAE,CAAC,CAAC;IAC/DA,UAAU,EAAEA,UAAU;IACtBC,OAAO,EAAE7D,UAAU,CAAC0D,UAAU,CAACG,OAAO,EAAED,UAAU,IAAIvC,YAAY,GAAG,MAAM,GAAG,MAAM,CAAC;IACrFyC,cAAc,EAAErC,KAAK,CAACC,OAAO,CAACgC,UAAU,CAACI,cAAc,CAAC,GAAGJ,UAAU,CAACI,cAAc,CAACnC,MAAM,GAAGtB,UAAU,CAACqD,UAAU,CAACI,cAAc,EAAE,CAAC,CAAC;IACtIC,YAAY,EAAEtC,KAAK,CAACC,OAAO,CAACgC,UAAU,CAACK,YAAY,CAAC,GAAGL,UAAU,CAACK,YAAY,CAACpC,MAAM,GAAGtB,UAAU,CAACqD,UAAU,CAACK,YAAY,EAAE,CAAC,CAAC;IAC9HjD,cAAc,EAAET,UAAU,CAACqD,UAAU,CAAC5C,cAAc,EAAE,CAAC,CAAC;IACxDkD,SAAS,EAAE3D,UAAU,CAACqD,UAAU,CAACM,SAAS,EAAE,CAAC,CAAC;IAC9CC,QAAQ,EAAE5D,UAAU,CAACqD,UAAU,CAACS,MAAM,IAAIT,UAAU,CAACO,QAAQ,EAAE,CAAC,CAAC;IACjEC,MAAM,EAAEN,UAAU,IAAIvC;EACxB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+C,aAAa,GAAGA,CAACV,UAAU,EAAErC,YAAY,GAAG,EAAE,KAAK;EAC9D,IAAI,CAACqC,UAAU,EAAE;IACf,OAAO;MACLW,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE,aAAa;MAC1BC,MAAM,EAAE,8BAA8B;MACtCC,WAAW,EAAE,uCAAuC;MACpDC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,MAAMC,UAAU,GAAGjB,qBAAqB,CAACC,UAAU,CAAC;EACpD,MAAMQ,MAAM,GAAGQ,UAAU,CAACd,UAAU,IAAIvC,YAAY;EAEpD,IAAI6C,MAAM,EAAE;IACV,OAAO;MACLG,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,cAAc;MAC3BC,MAAM,EAAE,gCAAgC;MACxCC,WAAW,EAAE,yCAAyC;MACtDC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLJ,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,YAAY;MACzBC,MAAM,EAAE,4BAA4B;MACpCC,WAAW,EAAE,qCAAqC;MAClDC,SAAS,EAAE;IACb,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,UAAU,GAAGA,CAAC1E,KAAK,EAAEC,QAAQ,GAAG,EAAE,KAAK;EAClD,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC1D,OAAOA,KAAK;EACd;EACA,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0E,UAAU,GAAIC,OAAO,IAAK;EACrC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC3E,UAAU,CAACwE,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;EACvD,MAAMI,gBAAgB,GAAG5E,UAAU,CAACwE,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE;EACpD,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;AACrE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAInF,KAAK,IAAK;EACvC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS;AAC7F,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoF,iBAAiB,GAAGA,CAACpF,KAAK,EAAEC,QAAQ,GAAG,EAAE,KAAK;EACzD,IAAIkF,cAAc,CAACnF,KAAK,CAAC,EAAE;IACzB,OAAOA,KAAK;EACd;EACA,IAAImF,cAAc,CAAClF,QAAQ,CAAC,EAAE;IAC5B,OAAOA,QAAQ;EACjB;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoF,eAAe,GAAIC,GAAG,IAAK;EACtC,OAAO9D,KAAK,CAACC,OAAO,CAAC6D,GAAG,CAAC,GAAGA,GAAG,CAAC5D,MAAM,GAAG,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6D,mBAAmB,GAAIC,GAAG,IAAK;EAC1C,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAAChE,KAAK,CAACC,OAAO,CAAC+D,GAAG,CAAC,GAAGvC,MAAM,CAACC,IAAI,CAACsC,GAAG,CAAC,CAAC9D,MAAM,GAAG,CAAC;AAC5F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}