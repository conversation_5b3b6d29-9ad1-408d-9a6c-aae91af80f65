{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck, TbBrain, TbTarget } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  onQuestionNavigate,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n  const questionData = question ? extractQuestionData(question) : null;\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer && answer.toString().trim() !== '');\n    if (onAnswerChange) {\n      onAnswerChange(answer);\n    }\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n  if (!question || !questionData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-blue-500 text-4xl mb-4\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Loading Question...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Please wait while the question loads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-1.5 bg-gray-200/60\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\",\n            style: {\n              width: `${progressPercentage}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\",\n                  children: examTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 font-medium\",\n                  children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${isTimeWarning ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200' : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: `w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-mono tracking-wider\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-xl\",\n                  children: questionIndex + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-white font-bold text-xl sm:text-2xl\",\n                  children: [\"Question \", questionIndex + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100 text-sm font-medium\",\n                  children: [\"of \", totalQuestions, \" questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/90 text-sm font-medium\",\n                children: \"Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-bold text-lg\",\n                children: [Math.round(progressPercentage), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 sm:p-8 lg:p-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-3 py-1 rounded-full text-xs font-semibold ${questionData.type === 'mcq' ? 'bg-blue-100 text-blue-700' : questionData.type === 'fill' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`,\n                  children: questionData.type === 'mcq' ? 'Multiple Choice' : questionData.type === 'fill' ? 'Free Text Answer' : 'Question'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 text-green-600 text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 23\n                  }, this), \"Answered\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\",\n                children: questionData.name || 'Loading question...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 mb-8\",\n            children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400 bg-gray-50 p-2 rounded\",\n              children: [\"Debug: Type=\", questionData.type, \", Options=\", Object.keys(questionData.options || {}).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ?\n            // Multiple Choice Questions\n            Object.entries(questionData.options).map(([key, value], index) => {\n              const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n              const label = optionLabels[index] || key;\n              const isSelected = currentAnswer === key;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleAnswerSelect(key),\n                className: `group cursor-pointer transition-all duration-300 ${isSelected ? 'transform scale-[1.02]' : 'hover:transform hover:scale-[1.01]'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${isSelected ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${isSelected ? 'bg-white/20 text-white' : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'}`,\n                      children: label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex-1 font-medium text-base sm:text-lg leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 27\n                    }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this);\n            }) : questionData.type === 'fill' || questionData.type === 'text' || !questionData.options || Object.keys(questionData.options).length === 0 ?\n            /*#__PURE__*/\n            // Free Text Input Questions\n            _jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border-2 border-blue-200 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                  children: \"Your Answer:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: currentAnswer || '',\n                  onChange: e => {\n                    console.log('✏️ Text input changed:', e.target.value);\n                    handleAnswerSelect(e.target.value);\n                  },\n                  onFocus: () => console.log('🎯 Text input focused'),\n                  onBlur: () => console.log('👋 Text input blurred'),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-4 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-300 resize-none text-gray-800 font-medium bg-white\",\n                  rows: 4,\n                  style: {\n                    minHeight: '120px',\n                    fontSize: '16px',\n                    // Prevents zoom on mobile\n                    lineHeight: '1.5'\n                  },\n                  autoComplete: \"off\",\n                  spellCheck: \"true\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\uD83D\\uDCA1 Tip: Be clear and concise in your answer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [(currentAnswer || '').length, \" characters\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-xs text-gray-400\",\n                  children: [\"Debug: Current value = \\\"\", currentAnswer || 'empty', \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // No options available\n            _jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400 text-4xl mb-4\",\n                children: \"\\uD83D\\uDCDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: [\"Question type: \", questionData.type || 'Unknown']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400 text-sm mt-2\",\n                children: \"No answer options available for this question.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 p-4 sm:p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-base sm:text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), \"Question Navigator\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2 mb-4\",\n              children: Array.from({\n                length: totalQuestions\n              }, (_, i) => {\n                const isCurrentQuestion = i === questionIndex;\n                const isAnswered = selectedOptions[i] && selectedOptions[i].toString().trim() !== '';\n                return /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    console.log('🎯 Navigating to question:', i + 1);\n                    if (typeof onQuestionNavigate === 'function') {\n                      onQuestionNavigate(i);\n                    } else {\n                      console.error('❌ onQuestionNavigate function not available');\n                    }\n                  },\n                  className: `w-10 h-10 rounded-lg font-bold text-sm transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-300 ${isCurrentQuestion ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg scale-110 ring-2 ring-blue-300' : isAnswered ? 'bg-green-500 text-white shadow-md hover:bg-green-600' : 'bg-white text-gray-600 border-2 border-gray-300 hover:bg-blue-50 hover:border-blue-400'}`,\n                  title: `Go to Question ${i + 1}${isCurrentQuestion ? ' (Current)' : ''}${isAnswered ? ' (Answered)' : ''}`,\n                  type: \"button\",\n                  children: i + 1\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-6 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Current\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 bg-green-500 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Answered\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 bg-white border border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Not Answered\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [Object.keys(selectedOptions).filter(key => selectedOptions[key] && selectedOptions[key].toString().trim() !== '').length, \" of \", totalQuestions, \" answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Debug Info:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), \" Q\", questionIndex + 1, \"/\", totalQuestions, \" | Type: \", questionData.type, \" | Answered: \", isAnswered ? 'Yes' : 'No', \" | Functions: \", onNext ? '✅' : '❌', \" Next, \", onPrevious ? '✅' : '❌', \" Prev, \", onQuestionNavigate ? '✅' : '❌', \" Nav\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sticky bottom-0 bg-white border-t-2 border-gray-200 pt-6 mt-8 z-20\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  console.log('🔙 Previous button clicked in renderer');\n                  if (onPrevious && questionIndex > 0) {\n                    onPrevious();\n                  }\n                },\n                disabled: questionIndex === 0,\n                className: `flex items-center gap-2 px-4 sm:px-6 py-3 rounded-xl font-semibold transition-all duration-300 border-2 ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50 border-gray-200' : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-gray-400 shadow-md hover:shadow-lg hover:scale-105'}`,\n                type: \"button\",\n                style: {\n                  minWidth: '120px',\n                  minHeight: '50px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: \"Prev\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: Array.from({\n                    length: Math.min(totalQuestions, 8)\n                  }, (_, i) => {\n                    // Show only first 8 questions as dots, or all if less than 8\n                    const actualIndex = totalQuestions <= 8 ? i : Math.floor(i * totalQuestions / 8);\n                    const isAnswered = selectedOptions[actualIndex] && selectedOptions[actualIndex].toString().trim() !== '';\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-3 h-3 rounded-full transition-all duration-300 ${actualIndex === questionIndex ? 'bg-blue-500 scale-125 ring-2 ring-blue-200' : isAnswered ? 'bg-green-500' : actualIndex < questionIndex ? 'bg-gray-400' : 'bg-gray-300'}`,\n                      title: `Question ${actualIndex + 1}${isAnswered ? ' (Answered)' : ''}`\n                    }, i, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-600 font-medium\",\n                  children: [questionIndex + 1, \" of \", totalQuestions]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  console.log('➡️ Next button clicked in renderer, current question:', questionIndex + 1);\n                  if (onNext) {\n                    onNext();\n                  } else {\n                    console.error('❌ onNext function not provided');\n                  }\n                },\n                className: \"flex items-center gap-2 px-4 sm:px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border-2 border-blue-500\",\n                type: \"button\",\n                style: {\n                  minWidth: '140px',\n                  minHeight: '50px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next Question'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: questionIndex === totalQuestions - 1 ? 'Finish' : 'Next'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, `modern-quiz-${renderKey}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernQuizRenderer, \"PH6vAwz56JboJBKDi/YYMWcz/c4=\");\n_c = ModernQuizRenderer;\nexport default ModernQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"ModernQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbBrain", "TbTarget", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "ModernQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "onQuestionNavigate", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionData", "name", "prev", "handleAnswerSelect", "answer", "toString", "trim", "progressPercentage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "Math", "round", "type", "process", "env", "NODE_ENV", "Object", "keys", "options", "length", "entries", "map", "key", "value", "index", "optionLabels", "label", "isSelected", "onClick", "onChange", "e", "console", "log", "target", "onFocus", "onBlur", "placeholder", "rows", "minHeight", "fontSize", "lineHeight", "autoComplete", "spell<PERSON>heck", "Array", "from", "_", "i", "isCurrentQuestion", "error", "title", "filter", "disabled", "min<PERSON><PERSON><PERSON>", "min", "actualIndex", "floor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Tb<PERSON><PERSON>,\n  TbArrowLeft,\n  TbArrowRight,\n  Tb<PERSON>heck,\n  TbBrain,\n  TbTarget\n} from 'react-icons/tb';\nimport {\n  extractQuestionData,\n  safeString,\n  formatTime\n} from '../utils/quizDataUtils';\n\nconst ModernQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  onQuestionNavigate,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [renderKey, setRenderKey] = useState(0);\n\n  const questionData = question ? extractQuestionData(question) : null;\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  useEffect(() => {\n    if (question && questionData && questionData.name) {\n      setRenderKey(prev => prev + 1);\n    }\n  }, [question, questionData]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer && answer.toString().trim() !== '');\n    if (onAnswerChange) {\n      onAnswerChange(answer);\n    }\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  if (!question || !questionData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto\">\n          <div className=\"text-blue-500 text-4xl mb-4\">⏳</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Loading Question...</h3>\n          <p className=\"text-gray-600 mb-4\">Please wait while the question loads.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div key={`modern-quiz-${renderKey}`} className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"w-full h-1.5 bg-gray-200/60\">\n            <div\n              className=\"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full transition-all duration-700 ease-out\"\n              style={{ width: `${progressPercentage}%` }}\n            />\n          </div>\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\">\n                  <TbBrain className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h1 className=\"text-lg sm:text-xl font-bold text-gray-900 truncate max-w-xs sm:max-w-md\">\n                    {examTitle}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 font-medium\">\n                    Question {questionIndex + 1} of {totalQuestions}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <div\n              className={`flex items-center gap-3 px-4 py-2.5 rounded-xl font-bold transition-all duration-300 ${\n                isTimeWarning\n                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-200'\n                  : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-200'\n              }`}\n            >\n              <TbClock className={`w-5 h-5 ${isTimeWarning ? 'animate-pulse' : ''}`} />\n              <span className=\"text-sm font-mono tracking-wider\">{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content Container */}\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\">\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500\">\n          {/* Question Header */}\n          <div className=\"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 px-6 sm:px-8 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-4\">\n                <div className=\"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm\">\n                  <span className=\"text-white font-bold text-xl\">{questionIndex + 1}</span>\n                </div>\n                <div>\n                  <h2 className=\"text-white font-bold text-xl sm:text-2xl\">Question {questionIndex + 1}</h2>\n                  <p className=\"text-blue-100 text-sm font-medium\">of {totalQuestions} questions</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-white/90 text-sm font-medium\">Progress</div>\n                <div className=\"text-white font-bold text-lg\">{Math.round(progressPercentage)}%</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"p-6 sm:p-8 lg:p-10\">\n            {/* Question Text */}\n            <div className=\"mb-8\">\n              <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-gray-200/50 shadow-sm\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                    questionData.type === 'mcq'\n                      ? 'bg-blue-100 text-blue-700'\n                      : questionData.type === 'fill'\n                      ? 'bg-green-100 text-green-700'\n                      : 'bg-gray-100 text-gray-700'\n                  }`}>\n                    {questionData.type === 'mcq' ? 'Multiple Choice' :\n                     questionData.type === 'fill' ? 'Free Text Answer' :\n                     'Question'}\n                  </div>\n                  {isAnswered && (\n                    <div className=\"flex items-center gap-1 text-green-600 text-sm font-medium\">\n                      <TbCheck className=\"w-4 h-4\" />\n                      Answered\n                    </div>\n                  )}\n                </div>\n                <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed\">\n                  {questionData.name || 'Loading question...'}\n                </h3>\n              </div>\n            </div>\n\n            {/* Answer Options */}\n            <div className=\"space-y-4 mb-8\">\n              {/* Debug info for question type */}\n              {process.env.NODE_ENV === 'development' && (\n                <div className=\"text-xs text-gray-400 bg-gray-50 p-2 rounded\">\n                  Debug: Type={questionData.type}, Options={Object.keys(questionData.options || {}).length}\n                </div>\n              )}\n\n              {questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ? (\n                // Multiple Choice Questions\n                Object.entries(questionData.options).map(([key, value], index) => {\n                  const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n                  const label = optionLabels[index] || key;\n                  const isSelected = currentAnswer === key;\n\n                  return (\n                    <div\n                      key={key}\n                      onClick={() => handleAnswerSelect(key)}\n                      className={`group cursor-pointer transition-all duration-300 ${\n                        isSelected\n                          ? 'transform scale-[1.02]'\n                          : 'hover:transform hover:scale-[1.01]'\n                      }`}\n                    >\n                      <div className={`p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${\n                        isSelected\n                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 shadow-xl shadow-blue-200'\n                          : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 shadow-md hover:shadow-lg'\n                      }`}>\n                        <div className=\"flex items-center gap-4\">\n                          <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${\n                            isSelected\n                              ? 'bg-white/20 text-white'\n                              : 'bg-blue-100 text-blue-600 group-hover:bg-blue-200'\n                          }`}>\n                            {label}\n                          </div>\n                          <span className=\"flex-1 font-medium text-base sm:text-lg leading-relaxed\">{value}</span>\n                          {isSelected && (\n                            <div className=\"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })\n              ) : questionData.type === 'fill' || questionData.type === 'text' ||\n                   (!questionData.options || Object.keys(questionData.options).length === 0) ? (\n                // Free Text Input Questions\n                <div className=\"space-y-4\">\n                  <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border-2 border-blue-200 shadow-sm\">\n                    <label className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                      Your Answer:\n                    </label>\n                    <textarea\n                      value={currentAnswer || ''}\n                      onChange={(e) => {\n                        console.log('✏️ Text input changed:', e.target.value);\n                        handleAnswerSelect(e.target.value);\n                      }}\n                      onFocus={() => console.log('🎯 Text input focused')}\n                      onBlur={() => console.log('👋 Text input blurred')}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-4 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-300 resize-none text-gray-800 font-medium bg-white\"\n                      rows={4}\n                      style={{\n                        minHeight: '120px',\n                        fontSize: '16px', // Prevents zoom on mobile\n                        lineHeight: '1.5'\n                      }}\n                      autoComplete=\"off\"\n                      spellCheck=\"true\"\n                    />\n                    <div className=\"mt-3 flex items-center justify-between\">\n                      <p className=\"text-sm text-gray-600\">\n                        💡 Tip: Be clear and concise in your answer\n                      </p>\n                      <span className=\"text-sm text-gray-500\">\n                        {(currentAnswer || '').length} characters\n                      </span>\n                    </div>\n                    {/* Test input to verify functionality */}\n                    <div className=\"mt-2 text-xs text-gray-400\">\n                      Debug: Current value = \"{currentAnswer || 'empty'}\"\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                // No options available\n                <div className=\"text-center py-8\">\n                  <div className=\"text-gray-400 text-4xl mb-4\">📝</div>\n                  <p className=\"text-gray-500\">\n                    Question type: {questionData.type || 'Unknown'}\n                  </p>\n                  <p className=\"text-gray-400 text-sm mt-2\">\n                    No answer options available for this question.\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* Question Navigation Grid */}\n            <div className=\"mb-6 p-4 sm:p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200 shadow-sm\">\n              <h4 className=\"text-base sm:text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\">\n                <TbTarget className=\"w-5 h-5 text-blue-600\" />\n                Question Navigator\n              </h4>\n              <div className=\"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2 mb-4\">\n                {Array.from({ length: totalQuestions }, (_, i) => {\n                  const isCurrentQuestion = i === questionIndex;\n                  const isAnswered = selectedOptions[i] && selectedOptions[i].toString().trim() !== '';\n\n                  return (\n                    <button\n                      key={i}\n                      onClick={() => {\n                        console.log('🎯 Navigating to question:', i + 1);\n                        if (typeof onQuestionNavigate === 'function') {\n                          onQuestionNavigate(i);\n                        } else {\n                          console.error('❌ onQuestionNavigate function not available');\n                        }\n                      }}\n                      className={`w-10 h-10 rounded-lg font-bold text-sm transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-300 ${\n                        isCurrentQuestion\n                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg scale-110 ring-2 ring-blue-300'\n                          : isAnswered\n                          ? 'bg-green-500 text-white shadow-md hover:bg-green-600'\n                          : 'bg-white text-gray-600 border-2 border-gray-300 hover:bg-blue-50 hover:border-blue-400'\n                      }`}\n                      title={`Go to Question ${i + 1}${isCurrentQuestion ? ' (Current)' : ''}${isAnswered ? ' (Answered)' : ''}`}\n                      type=\"button\"\n                    >\n                      {i + 1}\n                    </button>\n                  );\n                })}\n              </div>\n              <div className=\"mt-4 flex items-center justify-between\">\n                <div className=\"flex items-center gap-6 text-sm\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded\"></div>\n                    <span className=\"text-gray-600\">Current</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 bg-green-500 rounded\"></div>\n                    <span className=\"text-gray-600\">Answered</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 bg-white border border-gray-300 rounded\"></div>\n                    <span className=\"text-gray-600\">Not Answered</span>\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600\">\n                  {Object.keys(selectedOptions).filter(key => selectedOptions[key] && selectedOptions[key].toString().trim() !== '').length} of {totalQuestions} answered\n                </div>\n              </div>\n            </div>\n\n            {/* Debug Panel (Development Only) */}\n            {process.env.NODE_ENV === 'development' && (\n              <div className=\"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm\">\n                <strong>Debug Info:</strong> Q{questionIndex + 1}/{totalQuestions} |\n                Type: {questionData.type} |\n                Answered: {isAnswered ? 'Yes' : 'No'} |\n                Functions: {onNext ? '✅' : '❌'} Next, {onPrevious ? '✅' : '❌'} Prev, {onQuestionNavigate ? '✅' : '❌'} Nav\n              </div>\n            )}\n\n            {/* Navigation Buttons - Always Visible */}\n            <div className=\"sticky bottom-0 bg-white border-t-2 border-gray-200 pt-6 mt-8 z-20\">\n              <div className=\"flex items-center justify-between gap-4\">\n                {/* Previous Button */}\n                <button\n                  onClick={() => {\n                    console.log('🔙 Previous button clicked in renderer');\n                    if (onPrevious && questionIndex > 0) {\n                      onPrevious();\n                    }\n                  }}\n                  disabled={questionIndex === 0}\n                  className={`flex items-center gap-2 px-4 sm:px-6 py-3 rounded-xl font-semibold transition-all duration-300 border-2 ${\n                    questionIndex === 0\n                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50 border-gray-200'\n                      : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-gray-400 shadow-md hover:shadow-lg hover:scale-105'\n                  }`}\n                  type=\"button\"\n                  style={{ minWidth: '120px', minHeight: '50px' }}\n                >\n                  <TbArrowLeft className=\"w-5 h-5\" />\n                  <span className=\"hidden sm:inline\">Previous</span>\n                  <span className=\"sm:hidden\">Prev</span>\n                </button>\n\n                {/* Progress Indicator */}\n                <div className=\"flex flex-col items-center gap-2\">\n                  <div className=\"flex items-center gap-2\">\n                    {Array.from({ length: Math.min(totalQuestions, 8) }, (_, i) => {\n                      // Show only first 8 questions as dots, or all if less than 8\n                      const actualIndex = totalQuestions <= 8 ? i : Math.floor((i * totalQuestions) / 8);\n                      const isAnswered = selectedOptions[actualIndex] && selectedOptions[actualIndex].toString().trim() !== '';\n                      return (\n                        <div\n                          key={i}\n                          className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                            actualIndex === questionIndex\n                              ? 'bg-blue-500 scale-125 ring-2 ring-blue-200'\n                              : isAnswered\n                              ? 'bg-green-500'\n                              : actualIndex < questionIndex\n                              ? 'bg-gray-400'\n                              : 'bg-gray-300'\n                          }`}\n                          title={`Question ${actualIndex + 1}${isAnswered ? ' (Answered)' : ''}`}\n                        />\n                      );\n                    })}\n                  </div>\n                  <span className=\"text-xs text-gray-600 font-medium\">\n                    {questionIndex + 1} of {totalQuestions}\n                  </span>\n                </div>\n\n                {/* Next/Finish Button */}\n                <button\n                  onClick={() => {\n                    console.log('➡️ Next button clicked in renderer, current question:', questionIndex + 1);\n                    if (onNext) {\n                      onNext();\n                    } else {\n                      console.error('❌ onNext function not provided');\n                    }\n                  }}\n                  className=\"flex items-center gap-2 px-4 sm:px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border-2 border-blue-500\"\n                  type=\"button\"\n                  style={{ minWidth: '140px', minHeight: '50px' }}\n                >\n                  <span className=\"hidden sm:inline\">\n                    {questionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next Question'}\n                  </span>\n                  <span className=\"sm:hidden\">\n                    {questionIndex === totalQuestions - 1 ? 'Finish' : 'Next'}\n                  </span>\n                  {questionIndex === totalQuestions - 1 ? (\n                    <TbTarget className=\"w-5 h-5\" />\n                  ) : (\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SACEC,mBAAmB,EACnBC,UAAU,EACVC,UAAU,QACL,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EACpBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,kBAAkB;EAClBC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAACiB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAMiC,YAAY,GAAGnB,QAAQ,GAAGN,mBAAmB,CAACM,QAAQ,CAAC,GAAG,IAAI;EAEpEb,SAAS,CAAC,MAAM;IACd2B,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnCd,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,IAAImB,YAAY,IAAIA,YAAY,CAACC,IAAI,EAAE;MACjDF,YAAY,CAACG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACrB,QAAQ,EAAEmB,YAAY,CAAC,CAAC;EAE5B,MAAMG,kBAAkB,GAAIC,MAAM,IAAK;IACrCT,gBAAgB,CAACS,MAAM,CAAC;IACxBP,aAAa,CAAC,CAAC,CAACO,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAC1D,IAAIpB,cAAc,EAAE;MAClBA,cAAc,CAACkB,MAAM,CAAC;IACxB;EACF,CAAC;EAED,MAAMG,kBAAkB,GAAI,CAACzB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;EAEvE,IAAI,CAACF,QAAQ,IAAI,CAACmB,YAAY,EAAE;IAC9B,oBACErB,OAAA;MAAK6B,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG9B,OAAA;QAAK6B,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC9E9B,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDlC,OAAA;UAAI6B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFlC,OAAA;UAAG6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAsC6B,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAEtH9B,OAAA;MAAK6B,SAAS,EAAC,sFAAsF;MAAAC,QAAA,eACnG9B,OAAA;QAAK6B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9B,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C9B,OAAA;YACE6B,SAAS,EAAC,sHAAsH;YAChIM,KAAK,EAAE;cAAEC,KAAK,EAAG,GAAER,kBAAmB;YAAG;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD9B,OAAA;YAAK6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtC9B,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAK6B,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAC3H9B,OAAA,CAACN,OAAO;kBAACmC,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI6B,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,EACrFlB;gBAAS;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLlC,OAAA;kBAAG6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,WACtC,EAAC3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlC,OAAA;YACE6B,SAAS,EAAG,wFACVhB,aAAa,GACT,+EAA+E,GAC/E,uFACL,EAAE;YAAAiB,QAAA,gBAEH9B,OAAA,CAACV,OAAO;cAACuC,SAAS,EAAG,WAAUhB,aAAa,GAAG,eAAe,GAAG,EAAG;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzElC,OAAA;cAAM6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEhC,UAAU,CAACU,QAAQ;YAAC;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClE9B,OAAA;QAAK6B,SAAS,EAAC,wHAAwH;QAAAC,QAAA,gBAErI9B,OAAA;UAAK6B,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC5F9B,OAAA;YAAK6B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9B,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAK6B,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,eAClG9B,OAAA;kBAAM6B,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAE3B,aAAa,GAAG;gBAAC;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI6B,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,WAAS,EAAC3B,aAAa,GAAG,CAAC;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1FlC,OAAA;kBAAG6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,KAAG,EAAC1B,cAAc,EAAC,YAAU;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjElC,OAAA;gBAAK6B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GAAEO,IAAI,CAACC,KAAK,CAACV,kBAAkB,CAAC,EAAC,GAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAEjC9B,OAAA;YAAK6B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB9B,OAAA;cAAK6B,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAClH9B,OAAA;gBAAK6B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD9B,OAAA;kBAAK6B,SAAS,EAAG,gDACfR,YAAY,CAACkB,IAAI,KAAK,KAAK,GACvB,2BAA2B,GAC3BlB,YAAY,CAACkB,IAAI,KAAK,MAAM,GAC5B,6BAA6B,GAC7B,2BACL,EAAE;kBAAAT,QAAA,EACAT,YAAY,CAACkB,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC/ClB,YAAY,CAACkB,IAAI,KAAK,MAAM,GAAG,kBAAkB,GACjD;gBAAU;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,EACLjB,UAAU,iBACTjB,OAAA;kBAAK6B,SAAS,EAAC,4DAA4D;kBAAAC,QAAA,gBACzE9B,OAAA,CAACP,OAAO;oBAACoC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNlC,OAAA;gBAAI6B,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EACxFT,YAAY,CAACC,IAAI,IAAI;cAAqB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAE5BU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC1C,OAAA;cAAK6B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,cAChD,EAACT,YAAY,CAACkB,IAAI,EAAC,YAAU,EAACI,MAAM,CAACC,IAAI,CAACvB,YAAY,CAACwB,OAAO,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CACN,EAEAb,YAAY,CAACkB,IAAI,KAAK,KAAK,IAAIlB,YAAY,CAACwB,OAAO,IAAIF,MAAM,CAACC,IAAI,CAACvB,YAAY,CAACwB,OAAO,CAAC,CAACC,MAAM,GAAG,CAAC;YAClG;YACAH,MAAM,CAACI,OAAO,CAAC1B,YAAY,CAACwB,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;cAChE,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACnD,MAAMC,KAAK,GAAGD,YAAY,CAACD,KAAK,CAAC,IAAIF,GAAG;cACxC,MAAMK,UAAU,GAAGvC,aAAa,KAAKkC,GAAG;cAExC,oBACEjD,OAAA;gBAEEuD,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAACyB,GAAG,CAAE;gBACvCpB,SAAS,EAAG,oDACVyB,UAAU,GACN,wBAAwB,GACxB,oCACL,EAAE;gBAAAxB,QAAA,eAEH9B,OAAA;kBAAK6B,SAAS,EAAG,+DACfyB,UAAU,GACN,mGAAmG,GACnG,yGACL,EAAE;kBAAAxB,QAAA,eACD9B,OAAA;oBAAK6B,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtC9B,OAAA;sBAAK6B,SAAS,EAAG,uGACfyB,UAAU,GACN,wBAAwB,GACxB,mDACL,EAAE;sBAAAxB,QAAA,EACAuB;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlC,OAAA;sBAAM6B,SAAS,EAAC,yDAAyD;sBAAAC,QAAA,EAAEoB;oBAAK;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACvFoB,UAAU,iBACTtD,OAAA;sBAAK6B,SAAS,EAAC,mEAAmE;sBAAAC,QAAA,eAChF9B,OAAA,CAACP,OAAO;wBAACoC,SAAS,EAAC;sBAAoB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA5BDe,GAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BL,CAAC;YAEV,CAAC,CAAC,GACAb,YAAY,CAACkB,IAAI,KAAK,MAAM,IAAIlB,YAAY,CAACkB,IAAI,KAAK,MAAM,IAC1D,CAAClB,YAAY,CAACwB,OAAO,IAAIF,MAAM,CAACC,IAAI,CAACvB,YAAY,CAACwB,OAAO,CAAC,CAACC,MAAM,KAAK,CAAE;YAAA;YAC5E;YACA9C,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxB9B,OAAA;gBAAK6B,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,gBAC5G9B,OAAA;kBAAO6B,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlC,OAAA;kBACEkD,KAAK,EAAEnC,aAAa,IAAI,EAAG;kBAC3ByC,QAAQ,EAAGC,CAAC,IAAK;oBACfC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,CAAC,CAACG,MAAM,CAACV,KAAK,CAAC;oBACrD1B,kBAAkB,CAACiC,CAAC,CAACG,MAAM,CAACV,KAAK,CAAC;kBACpC,CAAE;kBACFW,OAAO,EAAEA,CAAA,KAAMH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE;kBACpDG,MAAM,EAAEA,CAAA,KAAMJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE;kBACnDI,WAAW,EAAC,0BAA0B;kBACtClC,SAAS,EAAC,qMAAqM;kBAC/MmC,IAAI,EAAE,CAAE;kBACR7B,KAAK,EAAE;oBACL8B,SAAS,EAAE,OAAO;oBAClBC,QAAQ,EAAE,MAAM;oBAAE;oBAClBC,UAAU,EAAE;kBACd,CAAE;kBACFC,YAAY,EAAC,KAAK;kBAClBC,UAAU,EAAC;gBAAM;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACFlC,OAAA;kBAAK6B,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD9B,OAAA;oBAAG6B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJlC,OAAA;oBAAM6B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACpC,CAACf,aAAa,IAAI,EAAE,EAAE+B,MAAM,EAAC,aAChC;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENlC,OAAA;kBAAK6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,2BAClB,EAACf,aAAa,IAAI,OAAO,EAAC,IACpD;gBAAA;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAlC,OAAA;cAAK6B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9B,OAAA;gBAAK6B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrDlC,OAAA;gBAAG6B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,iBACZ,EAACT,YAAY,CAACkB,IAAI,IAAI,SAAS;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACJlC,OAAA;gBAAG6B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,uGAAuG;YAAAC,QAAA,gBACpH9B,OAAA;cAAI6B,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAC3F9B,OAAA,CAACL,QAAQ;gBAACkC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlC,OAAA;cAAK6B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACxFwC,KAAK,CAACC,IAAI,CAAC;gBAAEzB,MAAM,EAAE1C;cAAe,CAAC,EAAE,CAACoE,CAAC,EAAEC,CAAC,KAAK;gBAChD,MAAMC,iBAAiB,GAAGD,CAAC,KAAKtE,aAAa;gBAC7C,MAAMc,UAAU,GAAGX,eAAe,CAACmE,CAAC,CAAC,IAAInE,eAAe,CAACmE,CAAC,CAAC,CAAC/C,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;gBAEpF,oBACE3B,OAAA;kBAEEuD,OAAO,EAAEA,CAAA,KAAM;oBACbG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEc,CAAC,GAAG,CAAC,CAAC;oBAChD,IAAI,OAAO9D,kBAAkB,KAAK,UAAU,EAAE;sBAC5CA,kBAAkB,CAAC8D,CAAC,CAAC;oBACvB,CAAC,MAAM;sBACLf,OAAO,CAACiB,KAAK,CAAC,6CAA6C,CAAC;oBAC9D;kBACF,CAAE;kBACF9C,SAAS,EAAG,0IACV6C,iBAAiB,GACb,kGAAkG,GAClGzD,UAAU,GACV,sDAAsD,GACtD,wFACL,EAAE;kBACH2D,KAAK,EAAG,kBAAiBH,CAAC,GAAG,CAAE,GAAEC,iBAAiB,GAAG,YAAY,GAAG,EAAG,GAAEzD,UAAU,GAAG,aAAa,GAAG,EAAG,EAAE;kBAC3GsB,IAAI,EAAC,QAAQ;kBAAAT,QAAA,EAEZ2C,CAAC,GAAG;gBAAC,GAnBDA,CAAC;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBA,CAAC;cAEb,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD9B,OAAA;gBAAK6B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C9B,OAAA;kBAAK6B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC9B,OAAA;oBAAK6B,SAAS,EAAC;kBAA8D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFlC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNlC,OAAA;kBAAK6B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC9B,OAAA;oBAAK6B,SAAS,EAAC;kBAA8B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDlC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNlC,OAAA;kBAAK6B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC9B,OAAA;oBAAK6B,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvElC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnCa,MAAM,CAACC,IAAI,CAACtC,eAAe,CAAC,CAACuE,MAAM,CAAC5B,GAAG,IAAI3C,eAAe,CAAC2C,GAAG,CAAC,IAAI3C,eAAe,CAAC2C,GAAG,CAAC,CAACvB,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAACmB,MAAM,EAAC,MAAI,EAAC1C,cAAc,EAAC,WAChJ;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC1C,OAAA;YAAK6B,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAChF9B,OAAA;cAAA8B,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAAC/B,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc,EAAC,WAC5D,EAACiB,YAAY,CAACkB,IAAI,EAAC,eACf,EAACtB,UAAU,GAAG,KAAK,GAAG,IAAI,EAAC,gBAC1B,EAACR,MAAM,GAAG,GAAG,GAAG,GAAG,EAAC,SAAO,EAACC,UAAU,GAAG,GAAG,GAAG,GAAG,EAAC,SAAO,EAACC,kBAAkB,GAAG,GAAG,GAAG,GAAG,EAAC,MACvG;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,eAGDlC,OAAA;YAAK6B,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjF9B,OAAA;cAAK6B,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAEtD9B,OAAA;gBACEuD,OAAO,EAAEA,CAAA,KAAM;kBACbG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;kBACrD,IAAIjD,UAAU,IAAIP,aAAa,GAAG,CAAC,EAAE;oBACnCO,UAAU,CAAC,CAAC;kBACd;gBACF,CAAE;gBACFoE,QAAQ,EAAE3E,aAAa,KAAK,CAAE;gBAC9B0B,SAAS,EAAG,2GACV1B,aAAa,KAAK,CAAC,GACf,yEAAyE,GACzE,yHACL,EAAE;gBACHoC,IAAI,EAAC,QAAQ;gBACbJ,KAAK,EAAE;kBAAE4C,QAAQ,EAAE,OAAO;kBAAEd,SAAS,EAAE;gBAAO,CAAE;gBAAAnC,QAAA,gBAEhD9B,OAAA,CAACT,WAAW;kBAACsC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnClC,OAAA;kBAAM6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDlC,OAAA;kBAAM6B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAGTlC,OAAA;gBAAK6B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C9B,OAAA;kBAAK6B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EACrCwC,KAAK,CAACC,IAAI,CAAC;oBAAEzB,MAAM,EAAET,IAAI,CAAC2C,GAAG,CAAC5E,cAAc,EAAE,CAAC;kBAAE,CAAC,EAAE,CAACoE,CAAC,EAAEC,CAAC,KAAK;oBAC7D;oBACA,MAAMQ,WAAW,GAAG7E,cAAc,IAAI,CAAC,GAAGqE,CAAC,GAAGpC,IAAI,CAAC6C,KAAK,CAAET,CAAC,GAAGrE,cAAc,GAAI,CAAC,CAAC;oBAClF,MAAMa,UAAU,GAAGX,eAAe,CAAC2E,WAAW,CAAC,IAAI3E,eAAe,CAAC2E,WAAW,CAAC,CAACvD,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;oBACxG,oBACE3B,OAAA;sBAEE6B,SAAS,EAAG,oDACVoD,WAAW,KAAK9E,aAAa,GACzB,4CAA4C,GAC5Cc,UAAU,GACV,cAAc,GACdgE,WAAW,GAAG9E,aAAa,GAC3B,aAAa,GACb,aACL,EAAE;sBACHyE,KAAK,EAAG,YAAWK,WAAW,GAAG,CAAE,GAAEhE,UAAU,GAAG,aAAa,GAAG,EAAG;oBAAE,GAVlEwD,CAAC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAWP,CAAC;kBAEN,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlC,OAAA;kBAAM6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAChD3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGNlC,OAAA;gBACEuD,OAAO,EAAEA,CAAA,KAAM;kBACbG,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAExD,aAAa,GAAG,CAAC,CAAC;kBACvF,IAAIM,MAAM,EAAE;oBACVA,MAAM,CAAC,CAAC;kBACV,CAAC,MAAM;oBACLiD,OAAO,CAACiB,KAAK,CAAC,gCAAgC,CAAC;kBACjD;gBACF,CAAE;gBACF9C,SAAS,EAAC,mQAAmQ;gBAC7QU,IAAI,EAAC,QAAQ;gBACbJ,KAAK,EAAE;kBAAE4C,QAAQ,EAAE,OAAO;kBAAEd,SAAS,EAAE;gBAAO,CAAE;gBAAAnC,QAAA,gBAEhD9B,OAAA;kBAAM6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC/B3B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;gBAAe;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACPlC,OAAA;kBAAM6B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACxB3B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;gBAAM;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,EACN/B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA,CAACL,QAAQ;kBAACkC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEhClC,OAAA,CAACR,YAAY;kBAACqC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,GA7VG,eAAcf,SAAU,EAAC;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA8V/B,CAAC;AAEV,CAAC;AAACpB,EAAA,CAtZIb,kBAAkB;AAAAkF,EAAA,GAAlBlF,kBAAkB;AAwZxB,eAAeA,kBAAkB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}