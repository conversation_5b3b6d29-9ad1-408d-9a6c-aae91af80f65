{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport { TbCheck, TbX, TbTrophy, TbBrain, TbTarget, TbRefresh, TbBulb } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { getQuizResult } from '../../../apicalls/quiz';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { extractQuizData, extractUserResultData, extractQuestionData, safeString, safeNumber, formatTime } from '../../../utils/quizDataUtils';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  var _location$state;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [result, setResult] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const resultFromState = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.result;\n\n  // Extract safe data to prevent object rendering errors\n  const examDataSafe = extractQuizData(examData);\n  const resultDataSafe = extractUserResultData(result);\n\n  // Debug result data\n  console.log('🎯 Quiz Result Page - Result data:', result);\n  console.log('💰 XP Data in result:', result === null || result === void 0 ? void 0 : result.xpData);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        var _examResponse$data;\n        setLoading(true);\n        dispatch(ShowLoading());\n\n        // Fetch exam data\n        const examResponse = await getExamById({\n          examId: id\n        });\n        if (!examResponse.success) {\n          message.error(examResponse.message);\n          navigate('/user/quiz');\n          return;\n        }\n        setExamData(examResponse.data);\n        setQuestions(((_examResponse$data = examResponse.data) === null || _examResponse$data === void 0 ? void 0 : _examResponse$data.questions) || []);\n\n        // If result is provided via state, use it\n        if (resultFromState) {\n          setResult(resultFromState);\n        } else {\n          // Otherwise, fetch the latest result for this quiz\n          const resultResponse = await getQuizResult(id);\n          console.log('Quiz result response:', resultResponse);\n          if (resultResponse.success) {\n            // Transform the report data to match expected result format\n            const report = resultResponse.data;\n            console.log('Quiz result data:', report);\n            const transformedResult = {\n              correctAnswers: report.correctAnswers || [],\n              wrongAnswers: report.wrongAnswers || [],\n              verdict: report.verdict,\n              score: report.percentage || report.score,\n              points: report.points || report.xpGained || (Array.isArray(report.correctAnswers) ? report.correctAnswers.length * 10 : 0),\n              totalQuestions: (Array.isArray(report.correctAnswers) ? report.correctAnswers.length : 0) + (Array.isArray(report.wrongAnswers) ? report.wrongAnswers.length : 0),\n              timeSpent: report.timeSpent || 0,\n              totalTimeAllowed: report.totalTimeAllowed || 0,\n              xpData: report.xpData\n            };\n            setResult(transformedResult);\n          } else {\n            console.error('Quiz result not found:', resultResponse.message);\n            message.error(`No quiz result found: ${resultResponse.message}. Please take the quiz first.`);\n            navigate('/user/quiz');\n            return;\n          }\n        }\n      } catch (error) {\n        message.error(error.message);\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n        dispatch(HideLoading());\n      }\n    };\n    if (id) {\n      fetchData();\n    }\n  }, [id, dispatch, navigate, resultFromState]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n\n          // Create enhanced sound effects using Web Audio API\n          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine'], staggerDelay = 0.15) => {\n            try {\n              const audioContext = new window.AudioContext();\n              frequencies.forEach((frequency, index) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                const delay = index * staggerDelay; // Customizable stagger timing\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);\n                oscillator.type = types[index] || types[0] || 'sine';\n                const volume = volumes[index] || volumes[0] || 0.3;\n                const duration = durations[index] || durations[0] || 0.5;\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);\n                oscillator.start(audioContext.currentTime + delay);\n                oscillator.stop(audioContext.currentTime + delay + duration);\n              });\n              return true;\n            } catch (error) {\n              console.log('Enhanced Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create chord sound (simultaneous notes) for richer harmonies\n          const createChordSound = (frequencies, duration, volume, type = 'sine') => {\n            try {\n              const audioContext = new window.AudioContext();\n              frequencies.forEach(frequency => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n                oscillator.type = type;\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n                oscillator.start(audioContext.currentTime);\n                oscillator.stop(audioContext.currentTime + duration);\n              });\n              return true;\n            } catch (error) {\n              console.log('Chord Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create triumphant celebration sound for perfect scores\n          const createCelebrationSound = () => {\n            const frequencies = [523, 659, 784, 1047, 1319]; // C5, E5, G5, C6, E6 - Major chord progression\n            const durations = [0.25, 0.25, 0.25, 0.4, 0.6];\n            const volumes = [0.45, 0.45, 0.45, 0.5, 0.55];\n            const types = ['sine', 'triangle', 'sine', 'triangle', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create excellent achievement sound - bright chord followed by ascending notes\n          const createExcellentSound = () => {\n            // Play a bright major chord first\n            createChordSound([440, 554, 659], 0.4, 0.3, 'triangle'); // A4, C#5, E5\n            // Follow with ascending melody\n            setTimeout(() => {\n              const frequencies = [659, 784]; // E5, G5\n              const durations = [0.3, 0.4];\n              const volumes = [0.35, 0.4];\n              const types = ['sine', 'sine'];\n              createEnhancedSound(frequencies, durations, volumes, types, 0.2);\n            }, 300);\n            return true;\n          };\n\n          // Create encouraging pass sound - warm and positive\n          const createPassSound = () => {\n            const frequencies = [349, 440]; // F4, A4 - Simple positive interval\n            const durations = [0.4, 0.5];\n            const volumes = [0.35, 0.4];\n            const types = ['sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create gentle encouragement sound for fails - supportive, not harsh\n          const createFailSound = () => {\n            const frequencies = [262, 294]; // C4, D4 - Gentle, hopeful interval\n            const durations = [0.5, 0.7];\n            const volumes = [0.3, 0.25];\n            const types = ['sine', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Play distinct sounds based on performance with unique characteristics\n          if (score === 100) {\n            // Perfect score - triumphant celebration with 5-note ascending sequence\n            createCelebrationSound();\n            // Add extra celebratory beeps\n            setTimeout(() => {\n              const frequencies = [1047, 1319, 1568]; // C6, E6, G6\n              const durations = [0.2, 0.2, 0.4];\n              const volumes = [0.3, 0.3, 0.35];\n              const types = ['triangle', 'triangle', 'triangle'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 800);\n            console.log('🏆 PERFECT SCORE! 🎉');\n          } else if (score >= 85) {\n            // Excellent - bright 3-note major chord\n            createExcellentSound();\n            console.log('🎉 EXCELLENT! ⭐');\n          } else if (score > 50) {\n            // Well Done - warm 2-note positive interval (only for >50%)\n            createPassSound();\n            console.log('✅ Well Done! 🚀');\n          } else {\n            // Fail - gentle 2-note supportive tone (different from pass)\n            createFailSound();\n            // Add a gentle follow-up note for encouragement\n            setTimeout(() => {\n              const frequencies = [330]; // E4 - hopeful note\n              const durations = [0.6];\n              const volumes = [0.25];\n              const types = ['sine'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 600);\n            console.log('💪 Keep Trying! 🌱');\n          }\n        } catch (error) {\n          console.log('Audio not supported:', error);\n          // Visual feedback as fallback\n          if (result.verdict === \"Pass\") {\n            console.log('🎉 Quiz Passed!');\n          } else {\n            console.log('💪 Keep trying!');\n          }\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [String(question)]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-700 mb-2\",\n          children: \"Loading Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Please wait while we fetch your quiz results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle missing result data\n  if (!result) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-700 mb-2\",\n          children: \"No Result Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-4\",\n          children: \"Unable to load quiz results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 85) return 'excellent'; // 85%+ gets excellent\n    if (score > 50) return 'good'; // Only >50% gets \"Well Done\" (more than half correct)\n    return 'fail';\n  };\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.score > 50,\n          // Only show confetti for >50% score\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n  const config = getPerformanceConfig();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [config.confetti && /*#__PURE__*/_jsxDEV(Confetti, {\n      width: width,\n      height: height\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`,\n              style: {\n                animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' : performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' : performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' : 'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',\n                transform: 'scale(1)',\n                transformOrigin: 'center'\n              },\n              children: [performanceLevel === 'perfect' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-full\",\n                  style: {\n                    background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',\n                    animation: 'sparkle 1.5s infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-2 right-2 w-3 h-3 bg-white rounded-full\",\n                  style: {\n                    animation: 'twinkle 1s infinite alternate'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full\",\n                  style: {\n                    animation: 'twinkle 1.2s infinite alternate-reverse'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(config.icon, {\n                className: \"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10\",\n                style: {\n                  filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative\",\n              style: {\n                animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' : performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' : performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' : 'titleShake 0.6s ease-in-out 2, failText 2s infinite',\n                color: 'white',\n                textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' : performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' : '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',\n                transformOrigin: 'center'\n              },\n              children: [config.title, performanceLevel === 'perfect' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full\",\n                  style: {\n                    animation: 'float 3s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full\",\n                  style: {\n                    animation: 'float 2.5s infinite ease-in-out 0.5s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full\",\n                  style: {\n                    animation: 'float 3.5s infinite ease-in-out 1s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg\",\n              style: {\n                color: 'white',\n                textShadow: '1px 1px 3px rgba(0,0,0,0.8)',\n                backgroundColor: 'rgba(0,0,0,0.3)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: config.subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\",\n              children: [resultDataSafe.score, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\",\n              children: resultDataSafe.correctAnswers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Correct\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\",\n              children: resultDataSafe.wrongAnswers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\",\n              children: [resultDataSafe.timeSpent, \"s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Time Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), result.xpData ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n            xpData: result.xpData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        /* Fallback XP Display */\n        _jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border-2 border-yellow-200 shadow-lg text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                className: \"w-8 h-8 text-yellow-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: \"XP Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-yellow-600 mb-2\",\n              children: [\"+\", resultDataSafe.xpGained]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Great job completing this quiz!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-6 sm:mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2\",\n                children: \"\\uD83D\\uDCDA Learning Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm sm:text-base\",\n                children: \"Review your answers and learn from explanations to improve your understanding\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6 sm:space-y-8\",\n              children: questions.map((question, index) => {\n                var _correctAnswersArray$, _wrongAnswersArray$fi;\n                // Safety check to prevent rendering objects\n                if (!question || typeof question !== 'object' || !question._id) {\n                  console.warn('Invalid question object at index:', index, question);\n                  return null;\n                }\n\n                // Extract safe question data\n                const questionData = extractQuestionData(question);\n                const correctAnswersArray = Array.isArray(result.correctAnswers) ? result.correctAnswers : [];\n                const wrongAnswersArray = Array.isArray(result.wrongAnswers) ? result.wrongAnswers : [];\n                const userAnswerRaw = ((_correctAnswersArray$ = correctAnswersArray.find(q => q._id === questionData.id)) === null || _correctAnswersArray$ === void 0 ? void 0 : _correctAnswersArray$.userAnswer) || ((_wrongAnswersArray$fi = wrongAnswersArray.find(q => q._id === questionData.id)) === null || _wrongAnswersArray$fi === void 0 ? void 0 : _wrongAnswersArray$fi.userAnswer) || \"\";\n                const userAnswerLetter = safeString(userAnswerRaw);\n                const isCorrect = correctAnswersArray.some(q => q._id === questionData.id);\n                const correctAnswerLetter = questionData.correctAnswer;\n\n                // Convert letter answers to actual option content\n                const getOptionContent = letter => {\n                  if (!letter || !questionData.options) return letter;\n                  return questionData.options[letter] || letter;\n                };\n                const userAnswer = getOptionContent(userAnswerLetter);\n                const correctAnswer = getOptionContent(correctAnswerLetter);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${isCorrect ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200' : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'} shadow-lg hover:shadow-xl hover:scale-[1.02]`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 sm:gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'}`,\n                        children: index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-lg sm:text-xl font-bold text-gray-900\",\n                          children: [\"Question \", index + 1]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 621,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 620,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                      children: isCorrect ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 634,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Correct\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 635,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 639,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Incorrect\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 640,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-5 sm:mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-5 sm:p-6 rounded-xl border-3 shadow-md ${isCorrect ? 'bg-white border-green-500' : 'bg-white border-red-500'}`,\n                      style: {\n                        backgroundColor: '#ffffff',\n                        border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',\n                        boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-gray-900 text-base sm:text-lg leading-relaxed font-bold\",\n                        style: {\n                          color: '#111827',\n                          fontWeight: '700',\n                          fontSize: '1.1rem',\n                          lineHeight: '1.7'\n                        },\n                        children: questionData.name || `Question ${index + 1}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 657,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-5 sm:mb-6 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: questionData.image,\n                        alt: \"Question Reference\",\n                        className: \"max-w-full max-h-40 sm:max-h-56 rounded-lg\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-medium text-gray-700 mb-1 text-sm flex items-center gap-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-600\",\n                          children: \"\\uD83D\\uDC64\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 686,\n                          columnNumber: 29\n                        }, this), \"Your Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex items-center gap-2 p-2 rounded-lg border text-sm ${isCorrect ? 'bg-green-50 text-green-800 border-green-300' : 'bg-red-50 text-red-800 border-red-300'}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0\",\n                          children: isCorrect ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-4 h-4 bg-green-500 rounded-full\",\n                            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                              className: \"w-3 h-3 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 697,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 696,\n                            columnNumber: 33\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-4 h-4 bg-red-500 rounded-full\",\n                            children: /*#__PURE__*/_jsxDEV(TbX, {\n                              className: \"w-3 h-3 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 701,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 700,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 694,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-gray-900 font-medium\",\n                            children: String(userAnswer || 'No answer provided')\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 706,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 705,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-medium text-gray-700 mb-1 text-sm flex items-center gap-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-green-600\",\n                          children: \"\\u2705\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 716,\n                          columnNumber: 29\n                        }, this), \"Correct Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 p-2 bg-green-50 text-green-800 rounded-lg border border-green-300 text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-4 h-4 bg-green-500 rounded-full\",\n                            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                              className: \"w-3 h-3 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 722,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 721,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 720,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-gray-900 font-medium\",\n                            children: String(correctAnswer || 'N/A')\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 726,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 725,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 719,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 25\n                    }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-4 sm:mt-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => fetchExplanation(String(question.name || 'Question'), String(correctAnswer || 'N/A'), String(userAnswer || ''), question.image),\n                        className: \"w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 745,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Get Explanation\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 746,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 736,\n                        columnNumber: 29\n                      }, this), explanations[String(question.name || 'Question')] && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4 p-5 sm:p-6 bg-blue-50 border-3 border-blue-400 rounded-xl shadow-md\",\n                        style: {\n                          border: '3px solid #3b82f6',\n                          boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-start gap-3 mb-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-10 h-10 bg-blue-500 rounded-full flex-shrink-0 shadow-lg\",\n                            children: /*#__PURE__*/_jsxDEV(TbBulb, {\n                              className: \"w-6 h-6 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 756,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 755,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"font-bold text-blue-900 text-lg sm:text-xl\",\n                            children: \"Explanation:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 758,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-gray-900 text-base sm:text-lg leading-relaxed font-medium\",\n                          style: {\n                            color: '#111827',\n                            fontWeight: '600',\n                            lineHeight: '1.7'\n                          },\n                          children: String(explanations[String(question.name || 'Question')] || '')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 760,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 27\n                    }, this), isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-center w-8 h-8 bg-green-500 rounded-full\",\n                          children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                            className: \"w-5 h-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 777,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"font-bold text-green-900 text-sm sm:text-base\",\n                            children: \"Great job!\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 780,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-700 text-xs sm:text-sm\",\n                            children: \"You demonstrated good understanding of this concept. Keep it up! \\uD83C\\uDF1F\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 781,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 779,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 775,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate(`/user/quiz/${id}/start`),\n            children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), \"Retake Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate('/user/quiz'),\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"More Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 405,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"8UtYMs2kaaOfEWLjEoxheULc/i0=\", false, function () {\n  return [useParams, useNavigate, useLocation, useDispatch, useWindowSize];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useDispatch", "message", "Confetti", "useWindowSize", "TbCheck", "TbX", "TbTrophy", "TbBrain", "TbTarget", "TbRefresh", "TbBulb", "getExamById", "chatWithChatGPTToExplainAns", "getQuizResult", "HideLoading", "ShowLoading", "extractQuizData", "extractUserResultData", "extractQuestionData", "safeString", "safeNumber", "formatTime", "XPResultDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "_location$state", "examData", "setExamData", "questions", "setQuestions", "explanations", "setExplanations", "result", "setResult", "loading", "setLoading", "id", "navigate", "location", "dispatch", "width", "height", "resultFromState", "state", "examDataSafe", "resultDataSafe", "console", "log", "xpData", "fetchData", "_examResponse$data", "examResponse", "examId", "success", "error", "data", "resultResponse", "report", "transformedResult", "correctAnswers", "wrongAnswers", "verdict", "score", "percentage", "points", "xpGained", "Array", "isArray", "length", "totalQuestions", "timeSpent", "totalTimeAllowed", "playSound", "createEnhancedSound", "frequencies", "durations", "volumes", "types", "stagger<PERSON><PERSON><PERSON>", "audioContext", "window", "AudioContext", "for<PERSON>ach", "frequency", "index", "oscillator", "createOscillator", "gainNode", "createGain", "delay", "connect", "destination", "setValueAtTime", "currentTime", "type", "volume", "duration", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createChordSound", "createCelebrationSound", "createExcellentSound", "setTimeout", "createPassSound", "createFailSound", "document", "body", "classList", "add", "remove", "fetchExplanation", "question", "expectedAnswer", "userAnswer", "imageUrl", "response", "prev", "String", "explanation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "getPerformanceLevel", "performanceLevel", "getPerformanceConfig", "bgGradient", "iconBg", "icon", "title", "subtitle", "confetti", "soundFile", "config", "style", "animation", "transform", "transform<PERSON><PERSON>in", "background", "filter", "color", "textShadow", "backgroundColor", "<PERSON><PERSON>ilter", "map", "_correctAnswersArray$", "_wrongAnswersArray$fi", "_id", "warn", "questionData", "correctAnswersArray", "wrongAnswersArray", "userAnswerRaw", "find", "q", "userAnswerLetter", "isCorrect", "some", "correctAnswerLetter", "<PERSON><PERSON><PERSON><PERSON>", "getOptionContent", "letter", "options", "border", "boxShadow", "fontWeight", "fontSize", "lineHeight", "name", "image", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport {\n  TbCheck,\n  TbX,\n  TbTrophy,\n  TbBrain,\n  TbTarget,\n  TbRefresh,\n  TbBulb\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { getQuizResult } from '../../../apicalls/quiz';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport {\n  extractQuizData,\n  extractUserResultData,\n  extractQuestionData,\n  safeString,\n  safeNumber,\n  formatTime\n} from '../../../utils/quizDataUtils';\n\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\n\nconst QuizResult = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [result, setResult] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { width, height } = useWindowSize();\n\n  const resultFromState = location.state?.result;\n\n  // Extract safe data to prevent object rendering errors\n  const examDataSafe = extractQuizData(examData);\n  const resultDataSafe = extractUserResultData(result);\n\n  // Debug result data\n  console.log('🎯 Quiz Result Page - Result data:', result);\n  console.log('💰 XP Data in result:', result?.xpData);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        dispatch(ShowLoading());\n\n        // Fetch exam data\n        const examResponse = await getExamById({ examId: id });\n\n        if (!examResponse.success) {\n          message.error(examResponse.message);\n          navigate('/user/quiz');\n          return;\n        }\n\n        setExamData(examResponse.data);\n        setQuestions(examResponse.data?.questions || []);\n\n        // If result is provided via state, use it\n        if (resultFromState) {\n          setResult(resultFromState);\n        } else {\n          // Otherwise, fetch the latest result for this quiz\n          const resultResponse = await getQuizResult(id);\n          console.log('Quiz result response:', resultResponse);\n\n          if (resultResponse.success) {\n            // Transform the report data to match expected result format\n            const report = resultResponse.data;\n            console.log('Quiz result data:', report);\n\n            const transformedResult = {\n              correctAnswers: report.correctAnswers || [],\n              wrongAnswers: report.wrongAnswers || [],\n              verdict: report.verdict,\n              score: report.percentage || report.score,\n              points: report.points || report.xpGained || (Array.isArray(report.correctAnswers) ? report.correctAnswers.length * 10 : 0),\n              totalQuestions: (Array.isArray(report.correctAnswers) ? report.correctAnswers.length : 0) + (Array.isArray(report.wrongAnswers) ? report.wrongAnswers.length : 0),\n              timeSpent: report.timeSpent || 0,\n              totalTimeAllowed: report.totalTimeAllowed || 0,\n              xpData: report.xpData\n            };\n            setResult(transformedResult);\n          } else {\n            console.error('Quiz result not found:', resultResponse.message);\n            message.error(`No quiz result found: ${resultResponse.message}. Please take the quiz first.`);\n            navigate('/user/quiz');\n            return;\n          }\n        }\n\n      } catch (error) {\n        message.error(error.message);\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n        dispatch(HideLoading());\n      }\n    };\n\n    if (id) {\n      fetchData();\n    }\n  }, [id, dispatch, navigate, resultFromState]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n\n          // Create enhanced sound effects using Web Audio API\n          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine'], staggerDelay = 0.15) => {\n            try {\n              const audioContext = new window.AudioContext();\n\n              frequencies.forEach((frequency, index) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                const delay = index * staggerDelay; // Customizable stagger timing\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);\n                oscillator.type = types[index] || types[0] || 'sine';\n\n                const volume = volumes[index] || volumes[0] || 0.3;\n                const duration = durations[index] || durations[0] || 0.5;\n\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);\n\n                oscillator.start(audioContext.currentTime + delay);\n                oscillator.stop(audioContext.currentTime + delay + duration);\n              });\n\n              return true;\n            } catch (error) {\n              console.log('Enhanced Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create chord sound (simultaneous notes) for richer harmonies\n          const createChordSound = (frequencies, duration, volume, type = 'sine') => {\n            try {\n              const audioContext = new window.AudioContext();\n\n              frequencies.forEach((frequency) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n                oscillator.type = type;\n\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n\n                oscillator.start(audioContext.currentTime);\n                oscillator.stop(audioContext.currentTime + duration);\n              });\n\n              return true;\n            } catch (error) {\n              console.log('Chord Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create triumphant celebration sound for perfect scores\n          const createCelebrationSound = () => {\n            const frequencies = [523, 659, 784, 1047, 1319]; // C5, E5, G5, C6, E6 - Major chord progression\n            const durations = [0.25, 0.25, 0.25, 0.4, 0.6];\n            const volumes = [0.45, 0.45, 0.45, 0.5, 0.55];\n            const types = ['sine', 'triangle', 'sine', 'triangle', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create excellent achievement sound - bright chord followed by ascending notes\n          const createExcellentSound = () => {\n            // Play a bright major chord first\n            createChordSound([440, 554, 659], 0.4, 0.3, 'triangle'); // A4, C#5, E5\n            // Follow with ascending melody\n            setTimeout(() => {\n              const frequencies = [659, 784]; // E5, G5\n              const durations = [0.3, 0.4];\n              const volumes = [0.35, 0.4];\n              const types = ['sine', 'sine'];\n              createEnhancedSound(frequencies, durations, volumes, types, 0.2);\n            }, 300);\n            return true;\n          };\n\n          // Create encouraging pass sound - warm and positive\n          const createPassSound = () => {\n            const frequencies = [349, 440]; // F4, A4 - Simple positive interval\n            const durations = [0.4, 0.5];\n            const volumes = [0.35, 0.4];\n            const types = ['sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create gentle encouragement sound for fails - supportive, not harsh\n          const createFailSound = () => {\n            const frequencies = [262, 294]; // C4, D4 - Gentle, hopeful interval\n            const durations = [0.5, 0.7];\n            const volumes = [0.3, 0.25];\n            const types = ['sine', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Play distinct sounds based on performance with unique characteristics\n          if (score === 100) {\n            // Perfect score - triumphant celebration with 5-note ascending sequence\n            createCelebrationSound();\n            // Add extra celebratory beeps\n            setTimeout(() => {\n              const frequencies = [1047, 1319, 1568]; // C6, E6, G6\n              const durations = [0.2, 0.2, 0.4];\n              const volumes = [0.3, 0.3, 0.35];\n              const types = ['triangle', 'triangle', 'triangle'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 800);\n            console.log('🏆 PERFECT SCORE! 🎉');\n          } else if (score >= 85) {\n            // Excellent - bright 3-note major chord\n            createExcellentSound();\n            console.log('🎉 EXCELLENT! ⭐');\n          } else if (score > 50) {\n            // Well Done - warm 2-note positive interval (only for >50%)\n            createPassSound();\n            console.log('✅ Well Done! 🚀');\n          } else {\n            // Fail - gentle 2-note supportive tone (different from pass)\n            createFailSound();\n            // Add a gentle follow-up note for encouragement\n            setTimeout(() => {\n              const frequencies = [330]; // E4 - hopeful note\n              const durations = [0.6];\n              const volumes = [0.25];\n              const types = ['sine'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 600);\n            console.log('💪 Keep Trying! 🌱');\n          }\n\n        } catch (error) {\n          console.log('Audio not supported:', error);\n          // Visual feedback as fallback\n          if (result.verdict === \"Pass\") {\n            console.log('🎉 Quiz Passed!');\n          } else {\n            console.log('💪 Keep trying!');\n          }\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setExplanations((prev) => ({ ...prev, [String(question)]: response.explanation }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">Loading Results</h2>\n          <p className=\"text-gray-500\">Please wait while we fetch your quiz results...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Handle missing result data\n  if (!result) {\n    return (\n      <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbTarget className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">No Result Data</h2>\n          <p className=\"text-gray-500 mb-4\">Unable to load quiz results.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 85) return 'excellent'; // 85%+ gets excellent\n    if (score > 50) return 'good'; // Only >50% gets \"Well Done\" (more than half correct)\n    return 'fail';\n  };\n\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.score > 50, // Only show confetti for >50% score\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n\n  const config = getPerformanceConfig();\n\n\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {config.confetti && <Confetti width={width} height={height} />}\n\n      {/* Main Content - Scrollable */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\">\n\n          {/* Hero Section with Performance Animation */}\n          <div className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`}>\n            {/* Animated Background Elements */}\n            <div className=\"absolute inset-0 overflow-hidden\">\n              <div className=\"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"></div>\n              <div className=\"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"></div>\n            </div>\n\n            <div className=\"relative z-10\">\n              {/* Animated Icon with Enhanced Effects */}\n              <div className={`inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`}\n                   style={{\n                     animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' :\n                               performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' :\n                               performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' :\n                               'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',\n                     transform: 'scale(1)',\n                     transformOrigin: 'center'\n                   }}>\n\n                {/* Sparkle Effects for Perfect Score */}\n                {performanceLevel === 'perfect' && (\n                  <>\n                    <div className=\"absolute inset-0 rounded-full\" style={{\n                      background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',\n                      animation: 'sparkle 1.5s infinite'\n                    }}></div>\n                    <div className=\"absolute top-2 right-2 w-3 h-3 bg-white rounded-full\" style={{\n                      animation: 'twinkle 1s infinite alternate'\n                    }}></div>\n                    <div className=\"absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full\" style={{\n                      animation: 'twinkle 1.2s infinite alternate-reverse'\n                    }}></div>\n                  </>\n                )}\n\n                <config.icon className=\"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10\"\n                            style={{\n                              filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'\n                            }} />\n              </div>\n\n              {/* Title with Enhanced Animation */}\n              <h1 className=\"text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative\"\n                  style={{\n                    animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' :\n                              performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' :\n                              performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' :\n                              'titleShake 0.6s ease-in-out 2, failText 2s infinite',\n                    color: 'white',\n                    textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' :\n                               performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' :\n                               '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',\n                    transformOrigin: 'center'\n                  }}>\n                {config.title}\n\n                {/* Floating particles for perfect score */}\n                {performanceLevel === 'perfect' && (\n                  <div className=\"absolute inset-0 pointer-events-none\">\n                    <div className=\"absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full\" style={{\n                      animation: 'float 3s infinite ease-in-out'\n                    }}></div>\n                    <div className=\"absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full\" style={{\n                      animation: 'float 2.5s infinite ease-in-out 0.5s'\n                    }}></div>\n                    <div className=\"absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full\" style={{\n                      animation: 'float 3.5s infinite ease-in-out 1s'\n                    }}></div>\n                  </div>\n                )}\n              </h1>\n\n              {/* Subtitle */}\n              <p className=\"text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg\"\n                 style={{\n                   color: 'white',\n                   textShadow: '1px 1px 3px rgba(0,0,0,0.8)',\n                   backgroundColor: 'rgba(0,0,0,0.3)',\n                   backdropFilter: 'blur(10px)'\n                 }}>\n                {config.subtitle}\n              </p>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\">\n            {/* Score Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\">\n                {resultDataSafe.score}%\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Score\n              </div>\n            </div>\n\n            {/* Correct Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\">\n                {resultDataSafe.correctAnswers}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Correct\n              </div>\n            </div>\n\n            {/* Wrong Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\">\n                {resultDataSafe.wrongAnswers}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Wrong\n              </div>\n            </div>\n\n            {/* Time Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\">\n                {resultDataSafe.timeSpent}s\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Time Spent\n              </div>\n            </div>\n          </div>\n\n          {/* XP Display */}\n          {result.xpData ? (\n            <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n              <XPResultDisplay xpData={result.xpData} />\n            </div>\n          ) : (\n            /* Fallback XP Display */\n            <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n              <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border-2 border-yellow-200 shadow-lg text-center\">\n                <div className=\"flex items-center justify-center mb-3\">\n                  <TbBulb className=\"w-8 h-8 text-yellow-600 mr-2\" />\n                  <h3 className=\"text-2xl font-bold text-gray-800\">XP Earned</h3>\n                </div>\n                <div className=\"text-4xl font-bold text-yellow-600 mb-2\">\n                  +{resultDataSafe.xpGained}\n                </div>\n                <p className=\"text-gray-600\">\n                  Great job completing this quiz!\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Enhanced Questions Summary for Learning */}\n          <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n            <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\">\n              <div className=\"text-center mb-6 sm:mb-8\">\n                <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2\">\n                  📚 Learning Summary\n                </h3>\n                <p className=\"text-gray-600 text-sm sm:text-base\">\n                  Review your answers and learn from explanations to improve your understanding\n                </p>\n              </div>\n\n              <div className=\"space-y-6 sm:space-y-8\">\n                {questions.map((question, index) => {\n                  // Safety check to prevent rendering objects\n                  if (!question || typeof question !== 'object' || !question._id) {\n                    console.warn('Invalid question object at index:', index, question);\n                    return null;\n                  }\n\n                  // Extract safe question data\n                  const questionData = extractQuestionData(question);\n\n                  const correctAnswersArray = Array.isArray(result.correctAnswers) ? result.correctAnswers : [];\n                  const wrongAnswersArray = Array.isArray(result.wrongAnswers) ? result.wrongAnswers : [];\n\n                  const userAnswerRaw = correctAnswersArray.find(q => q._id === questionData.id)?.userAnswer ||\n                                    wrongAnswersArray.find(q => q._id === questionData.id)?.userAnswer || \"\";\n                  const userAnswerLetter = safeString(userAnswerRaw);\n                  const isCorrect = correctAnswersArray.some(q => q._id === questionData.id);\n                  const correctAnswerLetter = questionData.correctAnswer;\n\n                  // Convert letter answers to actual option content\n                  const getOptionContent = (letter) => {\n                    if (!letter || !questionData.options) return letter;\n                    return questionData.options[letter] || letter;\n                  };\n\n                  const userAnswer = getOptionContent(userAnswerLetter);\n                  const correctAnswer = getOptionContent(correctAnswerLetter);\n\n                  return (\n                    <div key={index} className={`rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${\n                      isCorrect\n                        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200'\n                        : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'\n                    } shadow-lg hover:shadow-xl hover:scale-[1.02]`}>\n\n                      {/* Enhanced Question Header */}\n                      <div className=\"flex items-center justify-between mb-4 sm:mb-6\">\n                        <div className=\"flex items-center gap-3 sm:gap-4\">\n                          <div className={`flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${\n                            isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'\n                          }`}>\n                            {index + 1}\n                          </div>\n                          <div>\n                            <h4 className=\"text-lg sm:text-xl font-bold text-gray-900\">\n                              Question {index + 1}\n                            </h4>\n                          </div>\n                        </div>\n\n                        <div className={`flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${\n                          isCorrect\n                            ? 'bg-green-500 text-white'\n                            : 'bg-red-500 text-white'\n                        }`}>\n                          {isCorrect ? (\n                            <>\n                              <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Correct</span>\n                            </>\n                          ) : (\n                            <>\n                              <TbX className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Incorrect</span>\n                            </>\n                          )}\n                        </div>\n                      </div>\n\n                      {/* Question Display */}\n                      <div className=\"mb-5 sm:mb-6\">\n                        <div className={`p-5 sm:p-6 rounded-xl border-3 shadow-md ${\n                          isCorrect\n                            ? 'bg-white border-green-500'\n                            : 'bg-white border-red-500'\n                        }`} style={{\n                          backgroundColor: '#ffffff',\n                          border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',\n                          boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'\n                        }}>\n                          <div className=\"text-gray-900 text-base sm:text-lg leading-relaxed font-bold\" style={{\n                            color: '#111827',\n                            fontWeight: '700',\n                            fontSize: '1.1rem',\n                            lineHeight: '1.7'\n                          }}>\n                            {questionData.name || `Question ${index + 1}`}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Question Image */}\n                      {questionData.image && (\n                        <div className=\"mb-5 sm:mb-6 text-center\">\n                          <div className=\"inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm\">\n                            <img\n                              src={questionData.image}\n                              alt=\"Question Reference\"\n                              className=\"max-w-full max-h-40 sm:max-h-56 rounded-lg\"\n                            />\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Answer Analysis */}\n                      <div className=\"space-y-2\">\n                        {/* Your Answer */}\n                        <div>\n                          <h5 className=\"font-medium text-gray-700 mb-1 text-sm flex items-center gap-1\">\n                            <span className=\"text-blue-600\">👤</span>\n                            Your Answer:\n                          </h5>\n                          <div className={`flex items-center gap-2 p-2 rounded-lg border text-sm ${\n                            isCorrect\n                              ? 'bg-green-50 text-green-800 border-green-300'\n                              : 'bg-red-50 text-red-800 border-red-300'\n                          }`}>\n                            <div className=\"flex-shrink-0\">\n                              {isCorrect ? (\n                                <div className=\"flex items-center justify-center w-4 h-4 bg-green-500 rounded-full\">\n                                  <TbCheck className=\"w-3 h-3 text-white\" />\n                                </div>\n                              ) : (\n                                <div className=\"flex items-center justify-center w-4 h-4 bg-red-500 rounded-full\">\n                                  <TbX className=\"w-3 h-3 text-white\" />\n                                </div>\n                              )}\n                            </div>\n                            <div className=\"flex-1\">\n                              <div className=\"text-gray-900 font-medium\">\n                                {String(userAnswer || 'No answer provided')}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Correct Answer */}\n                        <div>\n                          <h5 className=\"font-medium text-gray-700 mb-1 text-sm flex items-center gap-1\">\n                            <span className=\"text-green-600\">✅</span>\n                            Correct Answer:\n                          </h5>\n                          <div className=\"flex items-center gap-2 p-2 bg-green-50 text-green-800 rounded-lg border border-green-300 text-sm\">\n                            <div className=\"flex-shrink-0\">\n                              <div className=\"flex items-center justify-center w-4 h-4 bg-green-500 rounded-full\">\n                                <TbCheck className=\"w-3 h-3 text-white\" />\n                              </div>\n                            </div>\n                            <div className=\"flex-1\">\n                              <div className=\"text-gray-900 font-medium\">\n                                {String(correctAnswer || 'N/A')}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* AI Explanation for Wrong Answers */}\n                        {!isCorrect && (\n                          <div className=\"mt-4 sm:mt-5\">\n                            <button\n                              onClick={() => fetchExplanation(\n                                String(question.name || 'Question'),\n                                String(correctAnswer || 'N/A'),\n                                String(userAnswer || ''),\n                                question.image\n                              )}\n                              className=\"w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl\"\n                            >\n                              <TbBulb className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Get Explanation</span>\n                            </button>\n\n                            {explanations[String(question.name || 'Question')] && (\n                              <div className=\"mt-4 p-5 sm:p-6 bg-blue-50 border-3 border-blue-400 rounded-xl shadow-md\" style={{\n                                border: '3px solid #3b82f6',\n                                boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2)'\n                              }}>\n                                <div className=\"flex items-start gap-3 mb-4\">\n                                  <div className=\"flex items-center justify-center w-10 h-10 bg-blue-500 rounded-full flex-shrink-0 shadow-lg\">\n                                    <TbBulb className=\"w-6 h-6 text-white\" />\n                                  </div>\n                                  <h6 className=\"font-bold text-blue-900 text-lg sm:text-xl\">Explanation:</h6>\n                                </div>\n                                <div className=\"text-gray-900 text-base sm:text-lg leading-relaxed font-medium\" style={{\n                                  color: '#111827',\n                                  fontWeight: '600',\n                                  lineHeight: '1.7'\n                                }}>\n                                  {String(explanations[String(question.name || 'Question')] || '')}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        )}\n\n                        {/* Encouragement for Correct Answers */}\n                        {isCorrect && (\n                          <div className=\"mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg\">\n                            <div className=\"flex items-center gap-3\">\n                              <div className=\"flex items-center justify-center w-8 h-8 bg-green-500 rounded-full\">\n                                <TbCheck className=\"w-5 h-5 text-white\" />\n                              </div>\n                              <div>\n                                <h6 className=\"font-bold text-green-900 text-sm sm:text-base\">Great job!</h6>\n                                <p className=\"text-green-700 text-xs sm:text-sm\">\n                                  You demonstrated good understanding of this concept. Keep it up! 🌟\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\">\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate(`/user/quiz/${id}/start`)}\n            >\n              <TbRefresh className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\" />\n              Retake Quiz\n            </button>\n\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate('/user/quiz')}\n            >\n              <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              <span className=\"hidden sm:inline\">More Quizzes</span>\n              <span className=\"sm:hidden\">More</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SACEC,eAAe,EACfC,qBAAqB,EACrBC,mBAAmB,EACnBC,UAAU,EACVC,UAAU,EACVC,UAAU,QACL,8BAA8B;AAErC,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM;IAAE6C;EAAG,CAAC,GAAG3C,SAAS,CAAC,CAAC;EAC1B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4C,KAAK;IAAEC;EAAO,CAAC,GAAG1C,aAAa,CAAC,CAAC;EAEzC,MAAM2C,eAAe,IAAAjB,eAAA,GAAGa,QAAQ,CAACK,KAAK,cAAAlB,eAAA,uBAAdA,eAAA,CAAgBO,MAAM;;EAE9C;EACA,MAAMY,YAAY,GAAGhC,eAAe,CAACc,QAAQ,CAAC;EAC9C,MAAMmB,cAAc,GAAGhC,qBAAqB,CAACmB,MAAM,CAAC;;EAEpD;EACAc,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEf,MAAM,CAAC;EACzDc,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,MAAM,CAAC;EAEpDxD,SAAS,CAAC,MAAM;IACd,MAAMyD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QAAA,IAAAC,kBAAA;QACFf,UAAU,CAAC,IAAI,CAAC;QAChBI,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;;QAEvB;QACA,MAAMwC,YAAY,GAAG,MAAM5C,WAAW,CAAC;UAAE6C,MAAM,EAAEhB;QAAG,CAAC,CAAC;QAEtD,IAAI,CAACe,YAAY,CAACE,OAAO,EAAE;UACzBxD,OAAO,CAACyD,KAAK,CAACH,YAAY,CAACtD,OAAO,CAAC;UACnCwC,QAAQ,CAAC,YAAY,CAAC;UACtB;QACF;QAEAV,WAAW,CAACwB,YAAY,CAACI,IAAI,CAAC;QAC9B1B,YAAY,CAAC,EAAAqB,kBAAA,GAAAC,YAAY,CAACI,IAAI,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBtB,SAAS,KAAI,EAAE,CAAC;;QAEhD;QACA,IAAIc,eAAe,EAAE;UACnBT,SAAS,CAACS,eAAe,CAAC;QAC5B,CAAC,MAAM;UACL;UACA,MAAMc,cAAc,GAAG,MAAM/C,aAAa,CAAC2B,EAAE,CAAC;UAC9CU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,cAAc,CAAC;UAEpD,IAAIA,cAAc,CAACH,OAAO,EAAE;YAC1B;YACA,MAAMI,MAAM,GAAGD,cAAc,CAACD,IAAI;YAClCT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEU,MAAM,CAAC;YAExC,MAAMC,iBAAiB,GAAG;cACxBC,cAAc,EAAEF,MAAM,CAACE,cAAc,IAAI,EAAE;cAC3CC,YAAY,EAAEH,MAAM,CAACG,YAAY,IAAI,EAAE;cACvCC,OAAO,EAAEJ,MAAM,CAACI,OAAO;cACvBC,KAAK,EAAEL,MAAM,CAACM,UAAU,IAAIN,MAAM,CAACK,KAAK;cACxCE,MAAM,EAAEP,MAAM,CAACO,MAAM,IAAIP,MAAM,CAACQ,QAAQ,KAAKC,KAAK,CAACC,OAAO,CAACV,MAAM,CAACE,cAAc,CAAC,GAAGF,MAAM,CAACE,cAAc,CAACS,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC;cAC1HC,cAAc,EAAE,CAACH,KAAK,CAACC,OAAO,CAACV,MAAM,CAACE,cAAc,CAAC,GAAGF,MAAM,CAACE,cAAc,CAACS,MAAM,GAAG,CAAC,KAAKF,KAAK,CAACC,OAAO,CAACV,MAAM,CAACG,YAAY,CAAC,GAAGH,MAAM,CAACG,YAAY,CAACQ,MAAM,GAAG,CAAC,CAAC;cACjKE,SAAS,EAAEb,MAAM,CAACa,SAAS,IAAI,CAAC;cAChCC,gBAAgB,EAAEd,MAAM,CAACc,gBAAgB,IAAI,CAAC;cAC9CvB,MAAM,EAAES,MAAM,CAACT;YACjB,CAAC;YACDf,SAAS,CAACyB,iBAAiB,CAAC;UAC9B,CAAC,MAAM;YACLZ,OAAO,CAACQ,KAAK,CAAC,wBAAwB,EAAEE,cAAc,CAAC3D,OAAO,CAAC;YAC/DA,OAAO,CAACyD,KAAK,CAAE,yBAAwBE,cAAc,CAAC3D,OAAQ,+BAA8B,CAAC;YAC7FwC,QAAQ,CAAC,YAAY,CAAC;YACtB;UACF;QACF;MAEF,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;QAC5BwC,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;QACjBI,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;MACzB;IACF,CAAC;IAED,IAAI0B,EAAE,EAAE;MACNa,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACb,EAAE,EAAEG,QAAQ,EAAEF,QAAQ,EAAEK,eAAe,CAAC,CAAC;;EAE7C;EACAlD,SAAS,CAAC,MAAM;IACd,IAAIwC,MAAM,EAAE;MACVc,OAAO,CAACC,GAAG,CAAE,QAAOf,MAAM,CAAC6B,OAAO,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAS,GAAE,CAAC;;MAEvE;MACA,MAAMW,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI;UACF,MAAMV,KAAK,GAAG9B,MAAM,CAAC8B,KAAK,IAAI,CAAC;;UAE/B;UACA,MAAMW,mBAAmB,GAAGA,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAEC,YAAY,GAAG,IAAI,KAAK;YAC9G,IAAI;cACF,MAAMC,YAAY,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAC,CAAC;cAE9CP,WAAW,CAACQ,OAAO,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;gBACxC,MAAMC,UAAU,GAAGN,YAAY,CAACO,gBAAgB,CAAC,CAAC;gBAClD,MAAMC,QAAQ,GAAGR,YAAY,CAACS,UAAU,CAAC,CAAC;gBAC1C,MAAMC,KAAK,GAAGL,KAAK,GAAGN,YAAY,CAAC,CAAC;;gBAEpCO,UAAU,CAACK,OAAO,CAACH,QAAQ,CAAC;gBAC5BA,QAAQ,CAACG,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;gBAE1CN,UAAU,CAACF,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEJ,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBAChFJ,UAAU,CAACS,IAAI,GAAGjB,KAAK,CAACO,KAAK,CAAC,IAAIP,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM;gBAEpD,MAAMkB,MAAM,GAAGnB,OAAO,CAACQ,KAAK,CAAC,IAAIR,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;gBAClD,MAAMoB,QAAQ,GAAGrB,SAAS,CAACS,KAAK,CAAC,IAAIT,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG;gBAExDY,QAAQ,CAACU,IAAI,CAACL,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBACjEF,QAAQ,CAACU,IAAI,CAACC,uBAAuB,CAACH,MAAM,EAAEhB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAG,IAAI,CAAC;gBACtFF,QAAQ,CAACU,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEpB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAGO,QAAQ,CAAC;gBAE9FX,UAAU,CAACe,KAAK,CAACrB,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBAClDJ,UAAU,CAACgB,IAAI,CAACtB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAGO,QAAQ,CAAC;cAC9D,CAAC,CAAC;cAEF,OAAO,IAAI;YACb,CAAC,CAAC,OAAO1C,KAAK,EAAE;cACdR,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,KAAK,CAAC;cAC5C,OAAO,KAAK;YACd;UACF,CAAC;;UAED;UACA,MAAMgD,gBAAgB,GAAGA,CAAC5B,WAAW,EAAEsB,QAAQ,EAAED,MAAM,EAAED,IAAI,GAAG,MAAM,KAAK;YACzE,IAAI;cACF,MAAMf,YAAY,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAC,CAAC;cAE9CP,WAAW,CAACQ,OAAO,CAAEC,SAAS,IAAK;gBACjC,MAAME,UAAU,GAAGN,YAAY,CAACO,gBAAgB,CAAC,CAAC;gBAClD,MAAMC,QAAQ,GAAGR,YAAY,CAACS,UAAU,CAAC,CAAC;gBAE1CH,UAAU,CAACK,OAAO,CAACH,QAAQ,CAAC;gBAC5BA,QAAQ,CAACG,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;gBAE1CN,UAAU,CAACF,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEJ,YAAY,CAACc,WAAW,CAAC;gBACxER,UAAU,CAACS,IAAI,GAAGA,IAAI;gBAEtBP,QAAQ,CAACU,IAAI,CAACL,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,CAAC;gBACzDN,QAAQ,CAACU,IAAI,CAACC,uBAAuB,CAACH,MAAM,EAAEhB,YAAY,CAACc,WAAW,GAAG,IAAI,CAAC;gBAC9EN,QAAQ,CAACU,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEpB,YAAY,CAACc,WAAW,GAAGG,QAAQ,CAAC;gBAEtFX,UAAU,CAACe,KAAK,CAACrB,YAAY,CAACc,WAAW,CAAC;gBAC1CR,UAAU,CAACgB,IAAI,CAACtB,YAAY,CAACc,WAAW,GAAGG,QAAQ,CAAC;cACtD,CAAC,CAAC;cAEF,OAAO,IAAI;YACb,CAAC,CAAC,OAAO1C,KAAK,EAAE;cACdR,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,KAAK,CAAC;cACzC,OAAO,KAAK;YACd;UACF,CAAC;;UAED;UACA,MAAMiD,sBAAsB,GAAGA,CAAA,KAAM;YACnC,MAAM7B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YACjD,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;YAC9C,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;YAC7C,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;YAC9D,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAM2B,oBAAoB,GAAGA,CAAA,KAAM;YACjC;YACAF,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;YACzD;YACAG,UAAU,CAAC,MAAM;cACf,MAAM/B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;cAC5B,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;cAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;cAC9BJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAE,GAAG,CAAC;YAClE,CAAC,EAAE,GAAG,CAAC;YACP,OAAO,IAAI;UACb,CAAC;;UAED;UACA,MAAM6B,eAAe,GAAGA,CAAA,KAAM;YAC5B,MAAMhC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;YAC5B,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;YAClC,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAM8B,eAAe,GAAGA,CAAA,KAAM;YAC5B,MAAMjC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;YAC5B,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;YAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;YAC9B,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,IAAIf,KAAK,KAAK,GAAG,EAAE;YACjB;YACAyC,sBAAsB,CAAC,CAAC;YACxB;YACAE,UAAU,CAAC,MAAM;cACf,MAAM/B,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;cACxC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACjC,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;cAChC,MAAMC,KAAK,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;cAClDJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;YAC7D,CAAC,EAAE,GAAG,CAAC;YACP/B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACrC,CAAC,MAAM,IAAIe,KAAK,IAAI,EAAE,EAAE;YACtB;YACA0C,oBAAoB,CAAC,CAAC;YACtB1D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM,IAAIe,KAAK,GAAG,EAAE,EAAE;YACrB;YACA4C,eAAe,CAAC,CAAC;YACjB5D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM;YACL;YACA4D,eAAe,CAAC,CAAC;YACjB;YACAF,UAAU,CAAC,MAAM;cACf,MAAM/B,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;cAC3B,MAAMC,SAAS,GAAG,CAAC,GAAG,CAAC;cACvB,MAAMC,OAAO,GAAG,CAAC,IAAI,CAAC;cACtB,MAAMC,KAAK,GAAG,CAAC,MAAM,CAAC;cACtBJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;YAC7D,CAAC,EAAE,GAAG,CAAC;YACP/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACnC;QAEF,CAAC,CAAC,OAAOO,KAAK,EAAE;UACdR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,KAAK,CAAC;UAC1C;UACA,IAAItB,MAAM,CAAC6B,OAAO,KAAK,MAAM,EAAE;YAC7Bf,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM;YACLD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC;QACF;MACF,CAAC;;MAED;MACA0D,UAAU,CAACjC,SAAS,EAAE,GAAG,CAAC;IAC5B;EACF,CAAC,EAAE,CAACxC,MAAM,CAAC,CAAC;EAEZxC,SAAS,CAAC,MAAM;IACdoH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACjF,IAAI;MACF9E,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM2G,QAAQ,GAAG,MAAM9G,2BAA2B,CAAC;QAAE0G,QAAQ;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAS,CAAC,CAAC;MACtG9E,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI4G,QAAQ,CAACjE,OAAO,EAAE;QACpBtB,eAAe,CAAEwF,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACC,MAAM,CAACN,QAAQ,CAAC,GAAGI,QAAQ,CAACG;QAAY,CAAC,CAAC,CAAC;MACpF,CAAC,MAAM;QACL5H,OAAO,CAACyD,KAAK,CAACgE,QAAQ,CAAChE,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdf,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,IAAIqC,OAAO,EAAE;IACX,oBACEd,OAAA;MAAKsG,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGvG,OAAA;QAAKsG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvG,OAAA;UAAKsG,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG3G,OAAA;UAAIsG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E3G,OAAA;UAAGsG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC/F,MAAM,EAAE;IACX,oBACEZ,OAAA;MAAKsG,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGvG,OAAA;QAAKsG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvG,OAAA,CAAChB,QAAQ;UAACsH,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D3G,OAAA;UAAIsG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E3G,OAAA;UAAGsG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClE3G,OAAA;UACE4G,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,YAAY,CAAE;UACtCqF,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMnE,KAAK,GAAG9B,MAAM,CAAC8B,KAAK,IAAI,CAAC;IAC/B,IAAIA,KAAK,KAAK,GAAG,EAAE,OAAO,SAAS;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,WAAW,CAAC,CAAC;IACrC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,MAAM,CAAC,CAAC;IAC/B,OAAO,MAAM;EACf,CAAC;EAED,MAAMoE,gBAAgB,GAAGD,mBAAmB,CAAC,CAAC;;EAE9C;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQD,gBAAgB;MACtB,KAAK,SAAS;QACZ,OAAO;UACLE,UAAU,EAAE,2CAA2C;UACvDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAEpI,QAAQ;UACdqI,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,wCAAwC;UAClDC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE;QACb,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACLN,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAEpI,QAAQ;UACdqI,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE;QACb,CAAC;MACH,KAAK,MAAM;QACT,OAAO;UACLN,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,6BAA6B;UACrCC,IAAI,EAAEtI,OAAO;UACbuI,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAEzG,MAAM,CAAC8B,KAAK,GAAG,EAAE;UAAE;UAC7B4E,SAAS,EAAE;QACb,CAAC;MACH;QACE,OAAO;UACLN,UAAU,EAAE,uCAAuC;UACnDC,MAAM,EAAE,0BAA0B;UAClCC,IAAI,EAAErI,GAAG;UACTsI,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,4CAA4C;UACtDC,QAAQ,EAAE,KAAK;UACfC,SAAS,EAAE;QACb,CAAC;IACL;EACF,CAAC;EAED,MAAMC,MAAM,GAAGR,oBAAoB,CAAC,CAAC;EAIrC,oBACE/G,OAAA;IAAKsG,SAAS,EAAC,qFAAqF;IAAAC,QAAA,GACjGgB,MAAM,CAACF,QAAQ,iBAAIrH,OAAA,CAACtB,QAAQ;MAAC0C,KAAK,EAAEA,KAAM;MAACC,MAAM,EAAEA;IAAO;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG9D3G,OAAA;MAAKsG,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCvG,OAAA;QAAKsG,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAG3EvG,OAAA;UAAKsG,SAAS,EAAG,qBAAoBiB,MAAM,CAACP,UAAW,sHAAsH;UAAAT,QAAA,gBAE3KvG,OAAA;YAAKsG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CvG,OAAA;cAAKsG,SAAS,EAAC;YAAsF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5G3G,OAAA;cAAKsG,SAAS,EAAC;YAAmG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eAEN3G,OAAA;YAAKsG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5BvG,OAAA;cAAKsG,SAAS,EAAG,uGAAsGiB,MAAM,CAACN,MAAO,gEAAgE;cAChMO,KAAK,EAAE;gBACLC,SAAS,EAAEX,gBAAgB,KAAK,SAAS,GAAG,iFAAiF,GACnHA,gBAAgB,KAAK,WAAW,GAAG,oDAAoD,GACvFA,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,MAAM,GAAG,+CAA+C,GAC5G,yDAAyD;gBACnEY,SAAS,EAAE,UAAU;gBACrBC,eAAe,EAAE;cACnB,CAAE;cAAApB,QAAA,GAGJO,gBAAgB,KAAK,SAAS,iBAC7B9G,OAAA,CAAAE,SAAA;gBAAAqG,QAAA,gBACEvG,OAAA;kBAAKsG,SAAS,EAAC,+BAA+B;kBAACkB,KAAK,EAAE;oBACpDI,UAAU,EAAE,oEAAoE;oBAChFH,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,sDAAsD;kBAACkB,KAAK,EAAE;oBAC3EC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,6DAA6D;kBAACkB,KAAK,EAAE;oBAClFC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACT,CACH,eAED3G,OAAA,CAACuH,MAAM,CAACL,IAAI;gBAACZ,SAAS,EAAC,oEAAoE;gBAC/EkB,KAAK,EAAE;kBACLK,MAAM,EAAEf,gBAAgB,KAAK,SAAS,GAAG,6CAA6C,GAAG;gBAC3F;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGN3G,OAAA;cAAIsG,SAAS,EAAC,mEAAmE;cAC7EkB,KAAK,EAAE;gBACLC,SAAS,EAAEX,gBAAgB,KAAK,SAAS,GAAG,oDAAoD,GACtFA,gBAAgB,KAAK,WAAW,GAAG,mDAAmD,GACtFA,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,MAAM,GAAG,+CAA+C,GAC5G,qDAAqD;gBAC/DgB,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAEjB,gBAAgB,KAAK,SAAS,GAAG,6DAA6D,GAC/FA,gBAAgB,KAAK,WAAW,GAAG,6DAA6D,GAChG,uDAAuD;gBAClEa,eAAe,EAAE;cACnB,CAAE;cAAApB,QAAA,GACHgB,MAAM,CAACJ,KAAK,EAGZL,gBAAgB,KAAK,SAAS,iBAC7B9G,OAAA;gBAAKsG,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,gBACnDvG,OAAA;kBAAKsG,SAAS,EAAC,4DAA4D;kBAACkB,KAAK,EAAE;oBACjFC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,0DAA0D;kBAACkB,KAAK,EAAE;oBAC/EC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,iEAAiE;kBAACkB,KAAK,EAAE;oBACtFC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGL3G,OAAA;cAAGsG,SAAS,EAAC,kEAAkE;cAC5EkB,KAAK,EAAE;gBACLM,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAE,6BAA6B;gBACzCC,eAAe,EAAE,iBAAiB;gBAClCC,cAAc,EAAE;cAClB,CAAE;cAAA1B,QAAA,EACFgB,MAAM,CAACH;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3G,OAAA;UAAKsG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAEnFvG,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,gEAAgE;cAAAC,QAAA,GAC5E9E,cAAc,CAACiB,KAAK,EAAC,GACxB;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC7E9E,cAAc,CAACc;YAAc;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC3E9E,cAAc,CAACe;YAAY;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,kEAAkE;cAAAC,QAAA,GAC9E9E,cAAc,CAACyB,SAAS,EAAC,GAC5B;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL/F,MAAM,CAACgB,MAAM,gBACZ5B,OAAA;UAAKsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvG,OAAA,CAACF,eAAe;YAAC8B,MAAM,EAAEhB,MAAM,CAACgB;UAAO;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;QAAA;QAEN;QACA3G,OAAA;UAAKsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvG,OAAA;YAAKsG,SAAS,EAAC,gHAAgH;YAAAC,QAAA,gBAC7HvG,OAAA;cAAKsG,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDvG,OAAA,CAACd,MAAM;gBAACoH,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD3G,OAAA;gBAAIsG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,yCAAyC;cAAAC,QAAA,GAAC,GACtD,EAAC9E,cAAc,CAACoB,QAAQ;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACN3G,OAAA;cAAGsG,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD3G,OAAA;UAAKsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvG,OAAA;YAAKsG,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7EvG,OAAA;cAAKsG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCvG,OAAA;gBAAIsG,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3G,OAAA;gBAAGsG,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN3G,OAAA;cAAKsG,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC/F,SAAS,CAAC0H,GAAG,CAAC,CAACpC,QAAQ,EAAE9B,KAAK,KAAK;gBAAA,IAAAmE,qBAAA,EAAAC,qBAAA;gBAClC;gBACA,IAAI,CAACtC,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,CAACuC,GAAG,EAAE;kBAC9D3G,OAAO,CAAC4G,IAAI,CAAC,mCAAmC,EAAEtE,KAAK,EAAE8B,QAAQ,CAAC;kBAClE,OAAO,IAAI;gBACb;;gBAEA;gBACA,MAAMyC,YAAY,GAAG7I,mBAAmB,CAACoG,QAAQ,CAAC;gBAElD,MAAM0C,mBAAmB,GAAG1F,KAAK,CAACC,OAAO,CAACnC,MAAM,CAAC2B,cAAc,CAAC,GAAG3B,MAAM,CAAC2B,cAAc,GAAG,EAAE;gBAC7F,MAAMkG,iBAAiB,GAAG3F,KAAK,CAACC,OAAO,CAACnC,MAAM,CAAC4B,YAAY,CAAC,GAAG5B,MAAM,CAAC4B,YAAY,GAAG,EAAE;gBAEvF,MAAMkG,aAAa,GAAG,EAAAP,qBAAA,GAAAK,mBAAmB,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKE,YAAY,CAACvH,EAAE,CAAC,cAAAmH,qBAAA,uBAAxDA,qBAAA,CAA0DnC,UAAU,OAAAoC,qBAAA,GACxEK,iBAAiB,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKE,YAAY,CAACvH,EAAE,CAAC,cAAAoH,qBAAA,uBAAtDA,qBAAA,CAAwDpC,UAAU,KAAI,EAAE;gBAC1F,MAAM6C,gBAAgB,GAAGlJ,UAAU,CAAC+I,aAAa,CAAC;gBAClD,MAAMI,SAAS,GAAGN,mBAAmB,CAACO,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKE,YAAY,CAACvH,EAAE,CAAC;gBAC1E,MAAMgI,mBAAmB,GAAGT,YAAY,CAACU,aAAa;;gBAEtD;gBACA,MAAMC,gBAAgB,GAAIC,MAAM,IAAK;kBACnC,IAAI,CAACA,MAAM,IAAI,CAACZ,YAAY,CAACa,OAAO,EAAE,OAAOD,MAAM;kBACnD,OAAOZ,YAAY,CAACa,OAAO,CAACD,MAAM,CAAC,IAAIA,MAAM;gBAC/C,CAAC;gBAED,MAAMnD,UAAU,GAAGkD,gBAAgB,CAACL,gBAAgB,CAAC;gBACrD,MAAMI,aAAa,GAAGC,gBAAgB,CAACF,mBAAmB,CAAC;gBAE3D,oBACEhJ,OAAA;kBAAiBsG,SAAS,EAAG,oFAC3BwC,SAAS,GACL,+FAA+F,GAC/F,oFACL,+CAA+C;kBAAAvC,QAAA,gBAG9CvG,OAAA;oBAAKsG,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC7DvG,OAAA;sBAAKsG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/CvG,OAAA;wBAAKsG,SAAS,EAAG,+HACfwC,SAAS,GAAG,iDAAiD,GAAG,4CACjE,EAAE;wBAAAvC,QAAA,EACAvC,KAAK,GAAG;sBAAC;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACN3G,OAAA;wBAAAuG,QAAA,eACEvG,OAAA;0BAAIsG,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,GAAC,WAChD,EAACvC,KAAK,GAAG,CAAC;wBAAA;0BAAAwC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN3G,OAAA;sBAAKsG,SAAS,EAAG,2GACfwC,SAAS,GACL,yBAAyB,GACzB,uBACL,EAAE;sBAAAvC,QAAA,EACAuC,SAAS,gBACR9I,OAAA,CAAAE,SAAA;wBAAAqG,QAAA,gBACEvG,OAAA,CAACpB,OAAO;0BAAC0H,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7C3G,OAAA;0BAAAuG,QAAA,EAAM;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACpB,CAAC,gBAEH3G,OAAA,CAAAE,SAAA;wBAAAqG,QAAA,gBACEvG,OAAA,CAACnB,GAAG;0BAACyH,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACzC3G,OAAA;0BAAAuG,QAAA,EAAM;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACtB;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN3G,OAAA;oBAAKsG,SAAS,EAAC,cAAc;oBAAAC,QAAA,eAC3BvG,OAAA;sBAAKsG,SAAS,EAAG,4CACfwC,SAAS,GACL,2BAA2B,GAC3B,yBACL,EAAE;sBAACtB,KAAK,EAAE;wBACTQ,eAAe,EAAE,SAAS;wBAC1BqB,MAAM,EAAEP,SAAS,GAAG,mBAAmB,GAAG,mBAAmB;wBAC7DQ,SAAS,EAAER,SAAS,GAAG,mCAAmC,GAAG;sBAC/D,CAAE;sBAAAvC,QAAA,eACAvG,OAAA;wBAAKsG,SAAS,EAAC,8DAA8D;wBAACkB,KAAK,EAAE;0BACnFM,KAAK,EAAE,SAAS;0BAChByB,UAAU,EAAE,KAAK;0BACjBC,QAAQ,EAAE,QAAQ;0BAClBC,UAAU,EAAE;wBACd,CAAE;wBAAAlD,QAAA,EACCgC,YAAY,CAACmB,IAAI,IAAK,YAAW1F,KAAK,GAAG,CAAE;sBAAC;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL4B,YAAY,CAACoB,KAAK,iBACjB3J,OAAA;oBAAKsG,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,eACvCvG,OAAA;sBAAKsG,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,eAC7FvG,OAAA;wBACE4J,GAAG,EAAErB,YAAY,CAACoB,KAAM;wBACxBE,GAAG,EAAC,oBAAoB;wBACxBvD,SAAS,EAAC;sBAA4C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGD3G,OAAA;oBAAKsG,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBAExBvG,OAAA;sBAAAuG,QAAA,gBACEvG,OAAA;wBAAIsG,SAAS,EAAC,gEAAgE;wBAAAC,QAAA,gBAC5EvG,OAAA;0BAAMsG,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,gBAE3C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL3G,OAAA;wBAAKsG,SAAS,EAAG,yDACfwC,SAAS,GACL,6CAA6C,GAC7C,uCACL,EAAE;wBAAAvC,QAAA,gBACDvG,OAAA;0BAAKsG,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAC3BuC,SAAS,gBACR9I,OAAA;4BAAKsG,SAAS,EAAC,oEAAoE;4BAAAC,QAAA,eACjFvG,OAAA,CAACpB,OAAO;8BAAC0H,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC,gBAEN3G,OAAA;4BAAKsG,SAAS,EAAC,kEAAkE;4BAAAC,QAAA,eAC/EvG,OAAA,CAACnB,GAAG;8BAACyH,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN3G,OAAA;0BAAKsG,SAAS,EAAC,QAAQ;0BAAAC,QAAA,eACrBvG,OAAA;4BAAKsG,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EACvCH,MAAM,CAACJ,UAAU,IAAI,oBAAoB;0BAAC;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN3G,OAAA;sBAAAuG,QAAA,gBACEvG,OAAA;wBAAIsG,SAAS,EAAC,gEAAgE;wBAAAC,QAAA,gBAC5EvG,OAAA;0BAAMsG,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,mBAE3C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL3G,OAAA;wBAAKsG,SAAS,EAAC,mGAAmG;wBAAAC,QAAA,gBAChHvG,OAAA;0BAAKsG,SAAS,EAAC,eAAe;0BAAAC,QAAA,eAC5BvG,OAAA;4BAAKsG,SAAS,EAAC,oEAAoE;4BAAAC,QAAA,eACjFvG,OAAA,CAACpB,OAAO;8BAAC0H,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN3G,OAAA;0BAAKsG,SAAS,EAAC,QAAQ;0BAAAC,QAAA,eACrBvG,OAAA;4BAAKsG,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EACvCH,MAAM,CAAC6C,aAAa,IAAI,KAAK;0BAAC;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAGL,CAACmC,SAAS,iBACT9I,OAAA;sBAAKsG,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BvG,OAAA;wBACE4G,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAC7BO,MAAM,CAACN,QAAQ,CAAC4D,IAAI,IAAI,UAAU,CAAC,EACnCtD,MAAM,CAAC6C,aAAa,IAAI,KAAK,CAAC,EAC9B7C,MAAM,CAACJ,UAAU,IAAI,EAAE,CAAC,EACxBF,QAAQ,CAAC6D,KACX,CAAE;wBACFrD,SAAS,EAAC,gTAAgT;wBAAAC,QAAA,gBAE1TvG,OAAA,CAACd,MAAM;0BAACoH,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5C3G,OAAA;0BAAAuG,QAAA,EAAM;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,EAERjG,YAAY,CAAC0F,MAAM,CAACN,QAAQ,CAAC4D,IAAI,IAAI,UAAU,CAAC,CAAC,iBAChD1J,OAAA;wBAAKsG,SAAS,EAAC,0EAA0E;wBAACkB,KAAK,EAAE;0BAC/F6B,MAAM,EAAE,mBAAmB;0BAC3BC,SAAS,EAAE;wBACb,CAAE;wBAAA/C,QAAA,gBACAvG,OAAA;0BAAKsG,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1CvG,OAAA;4BAAKsG,SAAS,EAAC,6FAA6F;4BAAAC,QAAA,eAC1GvG,OAAA,CAACd,MAAM;8BAACoH,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,eACN3G,OAAA;4BAAIsG,SAAS,EAAC,4CAA4C;4BAAAC,QAAA,EAAC;0BAAY;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzE,CAAC,eACN3G,OAAA;0BAAKsG,SAAS,EAAC,gEAAgE;0BAACkB,KAAK,EAAE;4BACrFM,KAAK,EAAE,SAAS;4BAChByB,UAAU,EAAE,KAAK;4BACjBE,UAAU,EAAE;0BACd,CAAE;0BAAAlD,QAAA,EACCH,MAAM,CAAC1F,YAAY,CAAC0F,MAAM,CAACN,QAAQ,CAAC4D,IAAI,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE;wBAAC;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN,EAGAmC,SAAS,iBACR9I,OAAA;sBAAKsG,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,eACxEvG,OAAA;wBAAKsG,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCvG,OAAA;0BAAKsG,SAAS,EAAC,oEAAoE;0BAAAC,QAAA,eACjFvG,OAAA,CAACpB,OAAO;4BAAC0H,SAAS,EAAC;0BAAoB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC,eACN3G,OAAA;0BAAAuG,QAAA,gBACEvG,OAAA;4BAAIsG,SAAS,EAAC,+CAA+C;4BAAAC,QAAA,EAAC;0BAAU;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC7E3G,OAAA;4BAAGsG,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAEjD;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAtLE3C,KAAK;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuLV,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3G,OAAA;UAAKsG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvG,OAAA;YACEsG,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAE,cAAaD,EAAG,QAAO,CAAE;YAAAuF,QAAA,gBAElDvG,OAAA,CAACf,SAAS;cAACqH,SAAS,EAAC;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE/G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3G,OAAA;YACEsG,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,YAAY,CAAE;YAAAsF,QAAA,gBAEtCvG,OAAA,CAACjB,OAAO;cAACuH,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1G3G,OAAA;cAAMsG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD3G,OAAA;cAAMsG,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACvG,EAAA,CArxBID,UAAU;EAAA,QAOC9B,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACFG,aAAa;AAAA;AAAAmL,EAAA,GAXnC3J,UAAU;AAuxBhB,eAAeA,UAAU;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}